Flow report for ZUOLAN_FPGA_OBJECT
Fri Aug 01 09:24:29 2025
Quartus Prime Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition


---------------------
; Table of Contents ;
---------------------
  1. Legal Notice
  2. Flow Summary
  3. Flow Settings
  4. Flow Non-Default Global Settings
  5. Flow Elapsed Time
  6. Flow OS Summary
  7. Flow Log
  8. Flow Messages
  9. Flow Suppressed Messages



----------------
; Legal Notice ;
----------------
Copyright (C) 2018  Intel Corporation. All rights reserved.
Your use of Intel Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Intel Program License 
Subscription Agreement, the Intel Quartus Prime License Agreement,
the Intel FPGA IP License Agreement, or other applicable license
agreement, including, without limitation, that your use is for
the sole purpose of programming logic devices manufactured by
Intel and sold by Intel or its authorized distributors.  Please
refer to the applicable agreement for further details.



+----------------------------------------------------------------------------------+
; Flow Summary                                                                     ;
+------------------------------------+---------------------------------------------+
; Flow Status                        ; Successful - Fri Aug 01 09:24:29 2025       ;
; Quartus Prime Version              ; 18.1.0 Build 625 09/12/2018 SJ Lite Edition ;
; Revision Name                      ; ZUOLAN_FPGA_OBJECT                          ;
; Top-level Entity Name              ; TOP                                         ;
; Family                             ; Cyclone IV E                                ;
; Device                             ; EP4CE10F17C8                                ;
; Timing Models                      ; Final                                       ;
; Total logic elements               ; 1,638 / 10,320 ( 16 % )                     ;
;     Total combinational functions  ; 1,331 / 10,320 ( 13 % )                     ;
;     Dedicated logic registers      ; 1,030 / 10,320 ( 10 % )                     ;
; Total registers                    ; 1030                                        ;
; Total pins                         ; 80 / 180 ( 44 % )                           ;
; Total virtual pins                 ; 0                                           ;
; Total memory bits                  ; 31,744 / 423,936 ( 7 % )                    ;
; Embedded Multiplier 9-bit elements ; 10 / 46 ( 22 % )                            ;
; Total PLLs                         ; 1 / 2 ( 50 % )                              ;
+------------------------------------+---------------------------------------------+


+-----------------------------------------+
; Flow Settings                           ;
+-------------------+---------------------+
; Option            ; Setting             ;
+-------------------+---------------------+
; Start date & time ; 08/01/2025 09:24:01 ;
; Main task         ; Compilation         ;
; Revision Name     ; ZUOLAN_FPGA_OBJECT  ;
+-------------------+---------------------+


+----------------------------------------------------------------------------------------------------------------------------------+
; Flow Non-Default Global Settings                                                                                                 ;
+-------------------------------------+----------------------------------------+--------------------+-------------+----------------+
; Assignment Name                     ; Value                                  ; Default Value      ; Entity Name ; Section Id     ;
+-------------------------------------+----------------------------------------+--------------------+-------------+----------------+
; COMPILER_SIGNATURE_ID               ; 4930992831105.175401144106916          ; --                 ; --          ; --             ;
; EDA_OUTPUT_DATA_FORMAT              ; Verilog Hdl                            ; --                 ; --          ; eda_simulation ;
; EDA_SIMULATION_TOOL                 ; ModelSim-Altera (Verilog)              ; <None>             ; --          ; --             ;
; EDA_TIME_SCALE                      ; 1 ps                                   ; --                 ; --          ; eda_simulation ;
; MAX_CORE_JUNCTION_TEMP              ; 85                                     ; --                 ; --          ; --             ;
; MIN_CORE_JUNCTION_TEMP              ; 0                                      ; --                 ; --          ; --             ;
; MISC_FILE                           ; ../ip/TYFIFO/TYFIFO.bsf                ; --                 ; --          ; --             ;
; MISC_FILE                           ; ../ip/triangle_rom/triangle_rom.bsf    ; --                 ; --          ; --             ;
; MISC_FILE                           ; ../ip/triangle_rom/triangle_rom_inst.v ; --                 ; --          ; --             ;
; MISC_FILE                           ; ../ip/triangle_rom/triangle_rom_bb.v   ; --                 ; --          ; --             ;
; MISC_FILE                           ; ../ip/sqaure_rom/sqaure_rom.bsf        ; --                 ; --          ; --             ;
; MISC_FILE                           ; ../ip/sqaure_rom/sqaure_rom_inst.v     ; --                 ; --          ; --             ;
; MISC_FILE                           ; ../ip/sqaure_rom/sqaure_rom_bb.v       ; --                 ; --          ; --             ;
; MISC_FILE                           ; ../ip/sawtooth_rom/sawtooth_rom.bsf    ; --                 ; --          ; --             ;
; MISC_FILE                           ; ../ip/sawtooth_rom/sawtooth_rom_inst.v ; --                 ; --          ; --             ;
; MISC_FILE                           ; ../ip/sawtooth_rom/sawtooth_rom_bb.v   ; --                 ; --          ; --             ;
; MISC_FILE                           ; ../ip/sin_rom/sin_rom.bsf              ; --                 ; --          ; --             ;
; MISC_FILE                           ; ../ip/sin_rom/sin_rom_inst.v           ; --                 ; --          ; --             ;
; MISC_FILE                           ; ../ip/sin_rom/sin_rom_bb.v             ; --                 ; --          ; --             ;
; MISC_FILE                           ; ../ip/MYPLL/MYPLL.bsf                  ; --                 ; --          ; --             ;
; MISC_FILE                           ; ../ip/MYPLL/MYPLL.ppf                  ; --                 ; --          ; --             ;
; NUM_PARALLEL_PROCESSORS             ; All                                    ; --                 ; --          ; --             ;
; OUTPUT_IO_TIMING_FAR_END_VMEAS      ; Half Signal Swing                      ; --                 ; --          ; --             ;
; OUTPUT_IO_TIMING_FAR_END_VMEAS      ; Half Signal Swing                      ; --                 ; --          ; --             ;
; OUTPUT_IO_TIMING_NEAR_END_VMEAS     ; Half Vccio                             ; --                 ; --          ; --             ;
; OUTPUT_IO_TIMING_NEAR_END_VMEAS     ; Half Vccio                             ; --                 ; --          ; --             ;
; PARTITION_COLOR                     ; -- (Not supported for targeted family) ; --                 ; TOP         ; Top            ;
; PARTITION_FITTER_PRESERVATION_LEVEL ; -- (Not supported for targeted family) ; --                 ; TOP         ; Top            ;
; PARTITION_NETLIST_TYPE              ; -- (Not supported for targeted family) ; --                 ; TOP         ; Top            ;
; POWER_BOARD_THERMAL_MODEL           ; None (CONSERVATIVE)                    ; --                 ; --          ; --             ;
; POWER_PRESET_COOLING_SOLUTION       ; 23 MM HEAT SINK WITH 200 LFPM AIRFLOW  ; --                 ; --          ; --             ;
; TOP_LEVEL_ENTITY                    ; TOP                                    ; ZUOLAN_FPGA_OBJECT ; --          ; --             ;
+-------------------------------------+----------------------------------------+--------------------+-------------+----------------+


+--------------------------------------------------------------------------------------------------------------------------+
; Flow Elapsed Time                                                                                                        ;
+----------------------+--------------+-------------------------+---------------------+------------------------------------+
; Module Name          ; Elapsed Time ; Average Processors Used ; Peak Virtual Memory ; Total CPU Time (on all processors) ;
+----------------------+--------------+-------------------------+---------------------+------------------------------------+
; Analysis & Synthesis ; 00:00:08     ; 1.0                     ; 4891 MB             ; 00:00:14                           ;
; Fitter               ; 00:00:13     ; 1.2                     ; 6348 MB             ; 00:00:30                           ;
; Assembler            ; 00:00:00     ; 1.0                     ; 4684 MB             ; 00:00:00                           ;
; Timing Analyzer      ; 00:00:02     ; 1.5                     ; 4906 MB             ; 00:00:02                           ;
; EDA Netlist Writer   ; 00:00:02     ; 1.0                     ; 4666 MB             ; 00:00:01                           ;
; Total                ; 00:00:25     ; --                      ; --                  ; 00:00:47                           ;
+----------------------+--------------+-------------------------+---------------------+------------------------------------+


+------------------------------------------------------------------------------------+
; Flow OS Summary                                                                    ;
+----------------------+------------------+------------+------------+----------------+
; Module Name          ; Machine Hostname ; OS Name    ; OS Version ; Processor type ;
+----------------------+------------------+------------+------------+----------------+
; Analysis & Synthesis ; DESKTOP-VQNNKAE  ; Windows 10 ; 10.0       ; x86_64         ;
; Fitter               ; DESKTOP-VQNNKAE  ; Windows 10 ; 10.0       ; x86_64         ;
; Assembler            ; DESKTOP-VQNNKAE  ; Windows 10 ; 10.0       ; x86_64         ;
; Timing Analyzer      ; DESKTOP-VQNNKAE  ; Windows 10 ; 10.0       ; x86_64         ;
; EDA Netlist Writer   ; DESKTOP-VQNNKAE  ; Windows 10 ; 10.0       ; x86_64         ;
+----------------------+------------------+------------+------------+----------------+


------------
; Flow Log ;
------------
quartus_map --read_settings_files=on --write_settings_files=off ZUOLAN_FPGA_OBJECT -c ZUOLAN_FPGA_OBJECT
quartus_fit --read_settings_files=off --write_settings_files=off ZUOLAN_FPGA_OBJECT -c ZUOLAN_FPGA_OBJECT
quartus_asm --read_settings_files=off --write_settings_files=off ZUOLAN_FPGA_OBJECT -c ZUOLAN_FPGA_OBJECT
quartus_sta ZUOLAN_FPGA_OBJECT -c ZUOLAN_FPGA_OBJECT
quartus_eda --read_settings_files=off --write_settings_files=off ZUOLAN_FPGA_OBJECT -c ZUOLAN_FPGA_OBJECT



