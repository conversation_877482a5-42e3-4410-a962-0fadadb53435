{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Design Software" 0 -1 1754011465515 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Timing Analyzer Quartus Prime " "Running Quartus Prime Timing Analyzer" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1754011465519 ""} { "Info" "IQEXE_START_BANNER_TIME" "Fri Aug 01 09:24:25 2025 " "Processing started: Fri Aug 01 09:24:25 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1754011465519 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Timing Analyzer" 0 -1 1754011465519 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_sta ZUOLAN_FPGA_OBJECT -c ZUOLAN_FPGA_OBJECT " "Command: quartus_sta ZUOLAN_FPGA_OBJECT -c ZUOLAN_FPGA_OBJECT" {  } {  } 0 0 "Command: %1!s!" 0 0 "Timing Analyzer" 0 -1 1754011465519 ""}
{ "Info" "0" "" "qsta_default_script.tcl version: #1" {  } {  } 0 0 "qsta_default_script.tcl version: #1" 0 0 "Timing Analyzer" 0 0 1754011465559 ""}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "14 14 " "Parallel compilation is enabled and will use 14 of the 14 processors detected" {  } {  } 0 20030 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "Timing Analyzer" 0 -1 1754011465681 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "Low junction temperature 0 degrees C " "Low junction temperature is 0 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754011465714 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "High junction temperature 85 degrees C " "High junction temperature is 85 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754011465714 ""}
{ "Warning" "WTDB_ANALYZE_COMB_LATCHES" "354 " "The Timing Analyzer is analyzing 354 combinational loops as latches. For more details, run the Check Timing command in the Timing Analyzer or view the \"User-Specified and Inferred Latches\" table in the Analysis & Synthesis report." {  } {  } 0 335093 "The Timing Analyzer is analyzing %1!d! combinational loops as latches. For more details, run the Check Timing command in the Timing Analyzer or view the \"User-Specified and Inferred Latches\" table in the Analysis & Synthesis report." 0 0 "Timing Analyzer" 0 -1 1754011465817 ""}
{ "Info" "ISTA_SDC_STATEMENT_PARENT" "" "Evaluating HDL-embedded SDC commands" { { "Info" "ISTA_SDC_STATEMENT_ENTITY" "dcfifo_vve1 " "Entity dcfifo_vve1" { { "Info" "ISTA_SDC_STATEMENT_EVAL" "set_false_path -from *rdptr_g* -to *ws_dgrp\|dffpipe_c09:dffpipe15\|dffe16a*  " "set_false_path -from *rdptr_g* -to *ws_dgrp\|dffpipe_c09:dffpipe15\|dffe16a* " {  } {  } 0 332166 "%1!s!" 0 0 "Design Software" 0 -1 1754011465845 ""} { "Info" "ISTA_SDC_STATEMENT_EVAL" "set_false_path -from *delayed_wrptr_g* -to *rs_dgwp\|dffpipe_b09:dffpipe12\|dffe13a*  " "set_false_path -from *delayed_wrptr_g* -to *rs_dgwp\|dffpipe_b09:dffpipe12\|dffe13a* " {  } {  } 0 332166 "%1!s!" 0 0 "Design Software" 0 -1 1754011465845 ""}  } {  } 0 332165 "Entity %1!s!" 0 0 "Design Software" 0 -1 1754011465845 ""}  } {  } 0 332164 "Evaluating HDL-embedded SDC commands" 0 0 "Timing Analyzer" 0 -1 1754011465845 ""}
{ "Critical Warning" "WSTA_SDC_NOT_FOUND" "ZUOLAN_FPGA_OBJECT.sdc " "Synopsys Design Constraints File file not found: 'ZUOLAN_FPGA_OBJECT.sdc'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." {  } {  } 1 332012 "Synopsys Design Constraints File file not found: '%1!s!'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." 0 0 "Timing Analyzer" 0 -1 1754011465848 ""}
{ "Info" "ISTA_NO_CLOCK_FOUND_DERIVING" "generated clocks \"derive_pll_clocks -create_base_clocks\" " "No user constrained generated clocks found in the design. Calling \"derive_pll_clocks -create_base_clocks\"" {  } {  } 0 332142 "No user constrained %1!s! found in the design. Calling %2!s!" 0 0 "Timing Analyzer" 0 -1 1754011465848 ""}
{ "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "Deriving PLL clocks " "Deriving PLL clocks" { { "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "create_clock -period 20.000 -waveform \{0.000 10.000\} -name CLK CLK " "create_clock -period 20.000 -waveform \{0.000 10.000\} -name CLK CLK" {  } {  } 0 332110 "%1!s!" 0 0 "Design Software" 0 -1 1754011465849 ""} { "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "create_generated_clock -source \{inst6\|altpll_component\|auto_generated\|pll1\|inclk\[0\]\} -multiply_by 3 -duty_cycle 50.00 -name \{inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} \{inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} " "create_generated_clock -source \{inst6\|altpll_component\|auto_generated\|pll1\|inclk\[0\]\} -multiply_by 3 -duty_cycle 50.00 -name \{inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} \{inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]\}" {  } {  } 0 332110 "%1!s!" 0 0 "Design Software" 0 -1 1754011465849 ""}  } {  } 0 332110 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1754011465849 ""}
{ "Info" "ISTA_NO_CLOCK_FOUND_DERIVING" "base clocks \"derive_clocks -period 1.0\" " "No user constrained base clocks found in the design. Calling \"derive_clocks -period 1.0\"" {  } {  } 0 332142 "No user constrained %1!s! found in the design. Calling %2!s!" 0 0 "Timing Analyzer" 0 -1 1754011465849 ""}
{ "Info" "ISTA_DERIVE_CLOCKS_INFO" "Deriving Clocks " "Deriving Clocks" { { "Info" "ISTA_DERIVE_CLOCKS_INFO" "create_clock -period 1.000 -name DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " "create_clock -period 1.000 -name DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A" {  } {  } 0 332105 "%1!s!" 0 0 "Design Software" 0 -1 1754011465850 ""} { "Info" "ISTA_DERIVE_CLOCKS_INFO" "create_clock -period 1.000 -name FPGA_CS_NEL FPGA_CS_NEL " "create_clock -period 1.000 -name FPGA_CS_NEL FPGA_CS_NEL" {  } {  } 0 332105 "%1!s!" 0 0 "Design Software" 0 -1 1754011465850 ""} { "Info" "ISTA_DERIVE_CLOCKS_INFO" "create_clock -period 1.000 -name AD2_INPUT_CLK AD2_INPUT_CLK " "create_clock -period 1.000 -name AD2_INPUT_CLK AD2_INPUT_CLK" {  } {  } 0 332105 "%1!s!" 0 0 "Design Software" 0 -1 1754011465850 ""} { "Info" "ISTA_DERIVE_CLOCKS_INFO" "create_clock -period 1.000 -name AD1_INPUT_CLK AD1_INPUT_CLK " "create_clock -period 1.000 -name AD1_INPUT_CLK AD1_INPUT_CLK" {  } {  } 0 332105 "%1!s!" 0 0 "Design Software" 0 -1 1754011465850 ""} { "Info" "ISTA_DERIVE_CLOCKS_INFO" "create_clock -period 1.000 -name FREQ_DEV:u_AD2_DEV\|FREQ_OUT FREQ_DEV:u_AD2_DEV\|FREQ_OUT " "create_clock -period 1.000 -name FREQ_DEV:u_AD2_DEV\|FREQ_OUT FREQ_DEV:u_AD2_DEV\|FREQ_OUT" {  } {  } 0 332105 "%1!s!" 0 0 "Design Software" 0 -1 1754011465850 ""} { "Info" "ISTA_DERIVE_CLOCKS_INFO" "create_clock -period 1.000 -name FREQ_DEV:u_AD1_DEV\|FREQ_OUT FREQ_DEV:u_AD1_DEV\|FREQ_OUT " "create_clock -period 1.000 -name FREQ_DEV:u_AD1_DEV\|FREQ_OUT FREQ_DEV:u_AD1_DEV\|FREQ_OUT" {  } {  } 0 332105 "%1!s!" 0 0 "Design Software" 0 -1 1754011465850 ""}  } {  } 0 332105 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1754011465850 ""}
{ "Info" "ISTA_NO_CLOCK_UNCERTAINTY_FOUND_DERIVING" "\"derive_clock_uncertainty\" " "No user constrained clock uncertainty found in the design. Calling \"derive_clock_uncertainty\"" {  } {  } 0 332143 "No user constrained clock uncertainty found in the design. Calling %1!s!" 0 0 "Timing Analyzer" 0 -1 1754011465856 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1754011465857 ""}
{ "Info" "0" "" "Found TIMING_ANALYZER_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON" {  } {  } 0 0 "Found TIMING_ANALYZER_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON" 0 0 "Timing Analyzer" 0 0 1754011465858 ""}
{ "Info" "0" "" "Analyzing Slow 1200mV 85C Model" {  } {  } 0 0 "Analyzing Slow 1200mV 85C Model" 0 0 "Timing Analyzer" 0 0 1754011465863 ""}
{ "Critical Warning" "WSTA_TIMING_NOT_MET" "" "Timing requirements not met" { { "Info" "ISTA_TIMING_NOT_MET_USE_ADA" "" "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." {  } {  } 0 11105 "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." 0 0 "Design Software" 0 -1 1754011465913 ""}  } {  } 1 332148 "Timing requirements not met" 0 0 "Timing Analyzer" 0 -1 1754011465913 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup -11.911 " "Worst-case setup slack is -11.911" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465914 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465914 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "  -11.911           -4222.510 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "  -11.911           -4222.510 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465914 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -7.365            -953.041 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -7.365            -953.041 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465914 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -6.824           -1128.153 FPGA_CS_NEL  " "   -6.824           -1128.153 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465914 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.552            -141.649 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "   -4.552            -141.649 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465914 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.529            -142.954 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "   -4.529            -142.954 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465914 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.010             -67.416 AD1_INPUT_CLK  " "   -3.010             -67.416 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465914 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -2.969             -71.834 AD2_INPUT_CLK  " "   -2.969             -71.834 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465914 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754011465914 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold -5.146 " "Worst-case hold slack is -5.146" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465921 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465921 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -5.146            -652.844 FPGA_CS_NEL  " "   -5.146            -652.844 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465921 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -0.871              -0.871 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -0.871              -0.871 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465921 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.441               0.000 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "    0.441               0.000 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465921 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.447               0.000 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "    0.447               0.000 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465921 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.465               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    0.465               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465921 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.497               0.000 AD2_INPUT_CLK  " "    0.497               0.000 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465921 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.756               0.000 AD1_INPUT_CLK  " "    0.756               0.000 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465921 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754011465921 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "recovery -6.691 " "Worst-case recovery slack is -6.691" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465922 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465922 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -6.691            -421.393 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   -6.691            -421.393 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465922 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -2.831             -60.822 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -2.831             -60.822 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465922 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -2.331             -71.200 AD2_INPUT_CLK  " "   -2.331             -71.200 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465922 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -2.055             -64.528 AD1_INPUT_CLK  " "   -2.055             -64.528 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465922 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754011465922 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "removal 2.271 " "Worst-case removal slack is 2.271" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465924 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465924 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    2.271               0.000 AD1_INPUT_CLK  " "    2.271               0.000 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465924 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    2.363               0.000 AD2_INPUT_CLK  " "    2.363               0.000 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465924 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    2.550               0.000 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "    2.550               0.000 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465924 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    5.355               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    5.355               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465924 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754011465924 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width -4.000 " "Worst-case minimum pulse width slack is -4.000" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465925 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465925 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.000            -583.844 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -4.000            -583.844 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465925 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.201            -666.708 FPGA_CS_NEL  " "   -3.201            -666.708 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465925 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.201            -106.939 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "   -3.201            -106.939 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465925 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.201            -106.939 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "   -3.201            -106.939 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465925 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000             -52.071 AD1_INPUT_CLK  " "   -3.000             -52.071 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465925 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000             -52.071 AD2_INPUT_CLK  " "   -3.000             -52.071 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465925 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    3.048               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    3.048               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465925 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.934               0.000 CLK  " "    9.934               0.000 CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011465925 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754011465925 ""}
{ "Info" "ISTA_REPORT_METASTABILITY_INFO" "Report Metastability: Found 96 synchronizer chains. " "Report Metastability: Found 96 synchronizer chains." { { "Info" "ISTA_REPORT_METASTABILITY_INFO" "Design MTBF is not calculated because the design doesn't meet its timing requirements. " "Design MTBF is not calculated because the design doesn't meet its timing requirements." {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1754011466048 ""}  } {  } 0 332114 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1754011466048 ""}
{ "Info" "0" "" "Analyzing Slow 1200mV 0C Model" {  } {  } 0 0 "Analyzing Slow 1200mV 0C Model" 0 0 "Timing Analyzer" 0 0 1754011466053 ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Timing Analyzer" 0 -1 1754011466064 ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Timing Analyzer" 0 -1 1754011466261 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1754011466324 ""}
{ "Critical Warning" "WSTA_TIMING_NOT_MET" "" "Timing requirements not met" { { "Info" "ISTA_TIMING_NOT_MET_USE_ADA" "" "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." {  } {  } 0 11105 "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." 0 0 "Design Software" 0 -1 1754011466338 ""}  } {  } 1 332148 "Timing requirements not met" 0 0 "Timing Analyzer" 0 -1 1754011466338 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup -11.240 " "Worst-case setup slack is -11.240" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466340 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466340 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "  -11.240           -3925.693 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "  -11.240           -3925.693 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466340 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -6.824           -1096.770 FPGA_CS_NEL  " "   -6.824           -1096.770 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466340 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -6.684            -875.162 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -6.684            -875.162 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466340 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.295            -134.180 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "   -4.295            -134.180 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466340 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.240            -131.069 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "   -4.240            -131.069 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466340 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -2.567             -57.268 AD1_INPUT_CLK  " "   -2.567             -57.268 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466340 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -2.523             -63.152 AD2_INPUT_CLK  " "   -2.523             -63.152 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466340 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754011466340 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold -4.638 " "Worst-case hold slack is -4.638" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466348 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466348 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.638            -591.210 FPGA_CS_NEL  " "   -4.638            -591.210 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466348 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -0.734              -0.734 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -0.734              -0.734 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466348 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.403               0.000 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "    0.403               0.000 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466348 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.403               0.000 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "    0.403               0.000 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466348 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.417               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    0.417               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466348 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.445               0.000 AD2_INPUT_CLK  " "    0.445               0.000 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466348 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.668               0.000 AD1_INPUT_CLK  " "    0.668               0.000 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466348 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754011466348 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "recovery -6.136 " "Worst-case recovery slack is -6.136" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466352 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466352 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -6.136            -385.432 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   -6.136            -385.432 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466352 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -2.620             -55.986 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -2.620             -55.986 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466352 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -2.053             -62.208 AD2_INPUT_CLK  " "   -2.053             -62.208 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466352 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.798             -56.352 AD1_INPUT_CLK  " "   -1.798             -56.352 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466352 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754011466352 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "removal 1.996 " "Worst-case removal slack is 1.996" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466355 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466355 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.996               0.000 AD1_INPUT_CLK  " "    1.996               0.000 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466355 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    2.054               0.000 AD2_INPUT_CLK  " "    2.054               0.000 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466355 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    2.327               0.000 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "    2.327               0.000 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466355 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    4.828               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    4.828               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466355 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754011466355 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width -4.000 " "Worst-case minimum pulse width slack is -4.000" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466359 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466359 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.000            -583.844 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -4.000            -583.844 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466359 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.201            -611.232 FPGA_CS_NEL  " "   -3.201            -611.232 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466359 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.201            -106.939 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "   -3.201            -106.939 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466359 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.201            -106.939 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "   -3.201            -106.939 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466359 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000             -52.413 AD2_INPUT_CLK  " "   -3.000             -52.413 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466359 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000             -52.071 AD1_INPUT_CLK  " "   -3.000             -52.071 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466359 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    3.049               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    3.049               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466359 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.943               0.000 CLK  " "    9.943               0.000 CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466359 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754011466359 ""}
{ "Info" "ISTA_REPORT_METASTABILITY_INFO" "Report Metastability: Found 96 synchronizer chains. " "Report Metastability: Found 96 synchronizer chains." { { "Info" "ISTA_REPORT_METASTABILITY_INFO" "Design MTBF is not calculated because the design doesn't meet its timing requirements. " "Design MTBF is not calculated because the design doesn't meet its timing requirements." {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1754011466526 ""}  } {  } 0 332114 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1754011466526 ""}
{ "Info" "0" "" "Analyzing Fast 1200mV 0C Model" {  } {  } 0 0 "Analyzing Fast 1200mV 0C Model" 0 0 "Timing Analyzer" 0 0 1754011466532 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1754011466611 ""}
{ "Critical Warning" "WSTA_TIMING_NOT_MET" "" "Timing requirements not met" { { "Info" "ISTA_TIMING_NOT_MET_USE_ADA" "" "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." {  } {  } 0 11105 "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." 0 0 "Design Software" 0 -1 1754011466617 ""}  } {  } 1 332148 "Timing requirements not met" 0 0 "Timing Analyzer" 0 -1 1754011466617 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup -5.306 " "Worst-case setup slack is -5.306" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466622 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466622 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -5.306           -1924.225 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   -5.306           -1924.225 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466622 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.120            -436.963 FPGA_CS_NEL  " "   -3.120            -436.963 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466622 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -2.642            -295.319 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -2.642            -295.319 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466622 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.480             -39.202 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "   -1.480             -39.202 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466622 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.433             -37.731 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "   -1.433             -37.731 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466622 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -0.803             -11.220 AD1_INPUT_CLK  " "   -0.803             -11.220 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466622 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -0.766             -12.671 AD2_INPUT_CLK  " "   -0.766             -12.671 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466622 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754011466622 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold -2.583 " "Worst-case hold slack is -2.583" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466632 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466632 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -2.583            -323.638 FPGA_CS_NEL  " "   -2.583            -323.638 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466632 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -0.597              -2.388 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -0.597              -2.388 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466632 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.154               0.000 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "    0.154               0.000 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466632 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.159               0.000 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "    0.159               0.000 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466632 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.194               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    0.194               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466632 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.208               0.000 AD2_INPUT_CLK  " "    0.208               0.000 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466632 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.301               0.000 AD1_INPUT_CLK  " "    0.301               0.000 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466632 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754011466632 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "recovery -3.132 " "Worst-case recovery slack is -3.132" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466638 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466638 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.132            -197.238 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   -3.132            -197.238 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466638 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -0.718             -14.442 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -0.718             -14.442 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466638 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -0.584             -17.424 AD2_INPUT_CLK  " "   -0.584             -17.424 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466638 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -0.446             -13.600 AD1_INPUT_CLK  " "   -0.446             -13.600 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466638 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754011466638 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "removal 1.026 " "Worst-case removal slack is 1.026" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466643 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466643 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.026               0.000 AD1_INPUT_CLK  " "    1.026               0.000 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466643 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.103               0.000 AD2_INPUT_CLK  " "    1.103               0.000 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466643 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.114               0.000 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "    1.114               0.000 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466643 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    2.414               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    2.414               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466643 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754011466643 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width -3.000 " "Worst-case minimum pulse width slack is -3.000" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466649 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466649 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000            -261.855 FPGA_CS_NEL  " "   -3.000            -261.855 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466649 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000             -42.039 AD1_INPUT_CLK  " "   -3.000             -42.039 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466649 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000             -41.719 AD2_INPUT_CLK  " "   -3.000             -41.719 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466649 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.000            -272.000 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -1.000            -272.000 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466649 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.000             -65.000 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "   -1.000             -65.000 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466649 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.000             -65.000 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "   -1.000             -65.000 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466649 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    3.129               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    3.129               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466649 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.594               0.000 CLK  " "    9.594               0.000 CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754011466649 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754011466649 ""}
{ "Info" "ISTA_REPORT_METASTABILITY_INFO" "Report Metastability: Found 96 synchronizer chains. " "Report Metastability: Found 96 synchronizer chains." { { "Info" "ISTA_REPORT_METASTABILITY_INFO" "Design MTBF is not calculated because the design doesn't meet its timing requirements. " "Design MTBF is not calculated because the design doesn't meet its timing requirements." {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1754011466855 ""}  } {  } 0 332114 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1754011466855 ""}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "setup " "Design is not fully constrained for setup requirements" {  } {  } 0 332102 "Design is not fully constrained for %1!s! requirements" 0 0 "Timing Analyzer" 0 -1 1754011467084 ""}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "hold " "Design is not fully constrained for hold requirements" {  } {  } 0 332102 "Design is not fully constrained for %1!s! requirements" 0 0 "Timing Analyzer" 0 -1 1754011467086 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Timing Analyzer 0 s 5 s Quartus Prime " "Quartus Prime Timing Analyzer was successful. 0 errors, 5 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4906 " "Peak virtual memory: 4906 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1754011467167 ""} { "Info" "IQEXE_END_BANNER_TIME" "Fri Aug 01 09:24:27 2025 " "Processing ended: Fri Aug 01 09:24:27 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1754011467167 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:02 " "Elapsed time: 00:00:02" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1754011467167 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:02 " "Total CPU time (on all processors): 00:00:02" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1754011467167 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Timing Analyzer" 0 -1 1754011467167 ""}
