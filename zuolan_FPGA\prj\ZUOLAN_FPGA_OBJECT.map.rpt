Analysis & Synthesis report for ZUOLAN_FPGA_OBJECT
Fri Aug 01 09:24:09 2025
Quartus Prime Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition


---------------------
; Table of Contents ;
---------------------
  1. Legal Notice
  2. Analysis & Synthesis Summary
  3. Analysis & Synthesis Settings
  4. Parallel Compilation
  5. Analysis & Synthesis Source Files Read
  6. Analysis & Synthesis Resource Usage Summary
  7. Analysis & Synthesis Resource Utilization by Entity
  8. Analysis & Synthesis RAM Summary
  9. Analysis & Synthesis DSP Block Usage Summary
 10. Analysis & Synthesis IP Cores Summary
 11. Registers Protected by Synthesis
 12. User-Specified and Inferred Latches
 13. Registers Removed During Synthesis
 14. General Register Statistics
 15. Inverted Register Statistics
 16. Multiplexer Restructuring Statistics (Restructuring Performed)
 17. Source assignments for TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component
 18. Source assignments for TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated
 19. Source assignments for TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|a_graycounter_4p6:rdptr_g1p
 20. Source assignments for TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|a_graycounter_07c:wrptr_g1p
 21. Source assignments for TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|altsyncram_ce41:fifo_ram
 22. Source assignments for TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp
 23. Source assignments for TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12
 24. Source assignments for TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp
 25. Source assignments for TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15
 26. Source assignments for TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component
 27. Source assignments for TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated
 28. Source assignments for TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|a_graycounter_4p6:rdptr_g1p
 29. Source assignments for TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|a_graycounter_07c:wrptr_g1p
 30. Source assignments for TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|altsyncram_ce41:fifo_ram
 31. Source assignments for TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp
 32. Source assignments for TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12
 33. Source assignments for TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp
 34. Source assignments for TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15
 35. Source assignments for DA_WAVEFORM:inst5|sin_rom:sin_rom_inst|altsyncram:altsyncram_component|altsyncram_hva1:auto_generated
 36. Source assignments for DA_WAVEFORM:inst5|sqaure_rom:sqaure_rom_inst|altsyncram:altsyncram_component|altsyncram_j6b1:auto_generated
 37. Source assignments for DA_WAVEFORM:inst5|triangle_rom:triangle_rom_inst|altsyncram:altsyncram_component|altsyncram_ocb1:auto_generated
 38. Source assignments for DA_WAVEFORM:inst5|sawtooth_rom:sawtooth_rom_inst|altsyncram:altsyncram_component|altsyncram_rdb1:auto_generated
 39. Source assignments for DA_WAVEFORM:inst8|sin_rom:sin_rom_inst|altsyncram:altsyncram_component|altsyncram_hva1:auto_generated
 40. Source assignments for DA_WAVEFORM:inst8|sqaure_rom:sqaure_rom_inst|altsyncram:altsyncram_component|altsyncram_j6b1:auto_generated
 41. Source assignments for DA_WAVEFORM:inst8|triangle_rom:triangle_rom_inst|altsyncram:altsyncram_component|altsyncram_ocb1:auto_generated
 42. Source assignments for DA_WAVEFORM:inst8|sawtooth_rom:sawtooth_rom_inst|altsyncram:altsyncram_component|altsyncram_rdb1:auto_generated
 43. Parameter Settings for User Entity Instance: DA_PARAMETER_CTRL:inst7
 44. Parameter Settings for User Entity Instance: MYPLL:inst6|altpll:altpll_component
 45. Parameter Settings for User Entity Instance: MASTER_CTRL:inst3
 46. Parameter Settings for User Entity Instance: AD_FREQ_MEASURE:u_AD_FREQ_MEASURE
 47. Parameter Settings for User Entity Instance: AD_DATA_DEAL:u_AD_DATA_DEAL
 48. Parameter Settings for User Entity Instance: TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component
 49. Parameter Settings for User Entity Instance: AD_FREQ_WORD:u_AD_FREQ_WORD
 50. Parameter Settings for User Entity Instance: TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component
 51. Parameter Settings for User Entity Instance: DA_PARAMETER_DEAL:inst1
 52. Parameter Settings for User Entity Instance: voltage_scaler_clocked_fast:inst10
 53. Parameter Settings for User Entity Instance: DA_WAVEFORM:inst5
 54. Parameter Settings for User Entity Instance: DA_WAVEFORM:inst5|sin_rom:sin_rom_inst|altsyncram:altsyncram_component
 55. Parameter Settings for User Entity Instance: DA_WAVEFORM:inst5|sqaure_rom:sqaure_rom_inst|altsyncram:altsyncram_component
 56. Parameter Settings for User Entity Instance: DA_WAVEFORM:inst5|triangle_rom:triangle_rom_inst|altsyncram:altsyncram_component
 57. Parameter Settings for User Entity Instance: DA_WAVEFORM:inst5|sawtooth_rom:sawtooth_rom_inst|altsyncram:altsyncram_component
 58. Parameter Settings for User Entity Instance: voltage_scaler_clocked_fast:inst14
 59. Parameter Settings for User Entity Instance: DA_WAVEFORM:inst8
 60. Parameter Settings for User Entity Instance: DA_WAVEFORM:inst8|sin_rom:sin_rom_inst|altsyncram:altsyncram_component
 61. Parameter Settings for User Entity Instance: DA_WAVEFORM:inst8|sqaure_rom:sqaure_rom_inst|altsyncram:altsyncram_component
 62. Parameter Settings for User Entity Instance: DA_WAVEFORM:inst8|triangle_rom:triangle_rom_inst|altsyncram:altsyncram_component
 63. Parameter Settings for User Entity Instance: DA_WAVEFORM:inst8|sawtooth_rom:sawtooth_rom_inst|altsyncram:altsyncram_component
 64. Parameter Settings for User Entity Instance: VOLTAGE_SCALER_CLOCKED:inst11
 65. Parameter Settings for User Entity Instance: VOLTAGE_SCALER_CLOCKED:inst12
 66. Parameter Settings for Inferred Entity Instance: voltage_scaler_clocked_fast:inst10|lpm_mult:Mult1
 67. Parameter Settings for Inferred Entity Instance: voltage_scaler_clocked_fast:inst14|lpm_mult:Mult1
 68. Parameter Settings for Inferred Entity Instance: voltage_scaler_clocked_fast:inst10|lpm_mult:Mult0
 69. Parameter Settings for Inferred Entity Instance: voltage_scaler_clocked_fast:inst14|lpm_mult:Mult0
 70. altpll Parameter Settings by Entity Instance
 71. dcfifo Parameter Settings by Entity Instance
 72. altsyncram Parameter Settings by Entity Instance
 73. lpm_mult Parameter Settings by Entity Instance
 74. Port Connectivity Checks: "DA_WAVEFORM:inst5|sawtooth_rom:sawtooth_rom_inst"
 75. Port Connectivity Checks: "DA_WAVEFORM:inst5|triangle_rom:triangle_rom_inst"
 76. Port Connectivity Checks: "DA_WAVEFORM:inst5|sqaure_rom:sqaure_rom_inst"
 77. Port Connectivity Checks: "DA_WAVEFORM:inst5|sin_rom:sin_rom_inst"
 78. Post-Synthesis Netlist Statistics for Top Partition
 79. Elapsed Time Per Partition
 80. Analysis & Synthesis Messages



----------------
; Legal Notice ;
----------------
Copyright (C) 2018  Intel Corporation. All rights reserved.
Your use of Intel Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Intel Program License 
Subscription Agreement, the Intel Quartus Prime License Agreement,
the Intel FPGA IP License Agreement, or other applicable license
agreement, including, without limitation, that your use is for
the sole purpose of programming logic devices manufactured by
Intel and sold by Intel or its authorized distributors.  Please
refer to the applicable agreement for further details.



+----------------------------------------------------------------------------------+
; Analysis & Synthesis Summary                                                     ;
+------------------------------------+---------------------------------------------+
; Analysis & Synthesis Status        ; Successful - Fri Aug 01 09:24:09 2025       ;
; Quartus Prime Version              ; 18.1.0 Build 625 09/12/2018 SJ Lite Edition ;
; Revision Name                      ; ZUOLAN_FPGA_OBJECT                          ;
; Top-level Entity Name              ; TOP                                         ;
; Family                             ; Cyclone IV E                                ;
; Total logic elements               ; 1,833                                       ;
;     Total combinational functions  ; 1,331                                       ;
;     Dedicated logic registers      ; 1,106                                       ;
; Total registers                    ; 1106                                        ;
; Total pins                         ; 80                                          ;
; Total virtual pins                 ; 0                                           ;
; Total memory bits                  ; 31,744                                      ;
; Embedded Multiplier 9-bit elements ; 10                                          ;
; Total PLLs                         ; 1                                           ;
+------------------------------------+---------------------------------------------+


+------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis Settings                                                                              ;
+------------------------------------------------------------------+--------------------+--------------------+
; Option                                                           ; Setting            ; Default Value      ;
+------------------------------------------------------------------+--------------------+--------------------+
; Device                                                           ; EP4CE10F17C8       ;                    ;
; Top-level entity name                                            ; TOP                ; ZUOLAN_FPGA_OBJECT ;
; Family name                                                      ; Cyclone IV E       ; Cyclone V          ;
; Maximum processors allowed for parallel compilation              ; All                ;                    ;
; Use smart compilation                                            ; Off                ; Off                ;
; Enable parallel Assembler and Timing Analyzer during compilation ; On                 ; On                 ;
; Enable compact report table                                      ; Off                ; Off                ;
; Restructure Multiplexers                                         ; Auto               ; Auto               ;
; Create Debugging Nodes for IP Cores                              ; Off                ; Off                ;
; Preserve fewer node names                                        ; On                 ; On                 ;
; Intel FPGA IP Evaluation Mode                                    ; Enable             ; Enable             ;
; Verilog Version                                                  ; Verilog_2001       ; Verilog_2001       ;
; VHDL Version                                                     ; VHDL_1993          ; VHDL_1993          ;
; State Machine Processing                                         ; Auto               ; Auto               ;
; Safe State Machine                                               ; Off                ; Off                ;
; Extract Verilog State Machines                                   ; On                 ; On                 ;
; Extract VHDL State Machines                                      ; On                 ; On                 ;
; Ignore Verilog initial constructs                                ; Off                ; Off                ;
; Iteration limit for constant Verilog loops                       ; 5000               ; 5000               ;
; Iteration limit for non-constant Verilog loops                   ; 250                ; 250                ;
; Add Pass-Through Logic to Inferred RAMs                          ; On                 ; On                 ;
; Infer RAMs from Raw Logic                                        ; On                 ; On                 ;
; Parallel Synthesis                                               ; On                 ; On                 ;
; DSP Block Balancing                                              ; Auto               ; Auto               ;
; NOT Gate Push-Back                                               ; On                 ; On                 ;
; Power-Up Don't Care                                              ; On                 ; On                 ;
; Remove Redundant Logic Cells                                     ; Off                ; Off                ;
; Remove Duplicate Registers                                       ; On                 ; On                 ;
; Ignore CARRY Buffers                                             ; Off                ; Off                ;
; Ignore CASCADE Buffers                                           ; Off                ; Off                ;
; Ignore GLOBAL Buffers                                            ; Off                ; Off                ;
; Ignore ROW GLOBAL Buffers                                        ; Off                ; Off                ;
; Ignore LCELL Buffers                                             ; Off                ; Off                ;
; Ignore SOFT Buffers                                              ; On                 ; On                 ;
; Limit AHDL Integers to 32 Bits                                   ; Off                ; Off                ;
; Optimization Technique                                           ; Balanced           ; Balanced           ;
; Carry Chain Length                                               ; 70                 ; 70                 ;
; Auto Carry Chains                                                ; On                 ; On                 ;
; Auto Open-Drain Pins                                             ; On                 ; On                 ;
; Perform WYSIWYG Primitive Resynthesis                            ; Off                ; Off                ;
; Auto ROM Replacement                                             ; On                 ; On                 ;
; Auto RAM Replacement                                             ; On                 ; On                 ;
; Auto DSP Block Replacement                                       ; On                 ; On                 ;
; Auto Shift Register Replacement                                  ; Auto               ; Auto               ;
; Allow Shift Register Merging across Hierarchies                  ; Auto               ; Auto               ;
; Auto Clock Enable Replacement                                    ; On                 ; On                 ;
; Strict RAM Replacement                                           ; Off                ; Off                ;
; Allow Synchronous Control Signals                                ; On                 ; On                 ;
; Force Use of Synchronous Clear Signals                           ; Off                ; Off                ;
; Auto RAM Block Balancing                                         ; On                 ; On                 ;
; Auto RAM to Logic Cell Conversion                                ; Off                ; Off                ;
; Auto Resource Sharing                                            ; Off                ; Off                ;
; Allow Any RAM Size For Recognition                               ; Off                ; Off                ;
; Allow Any ROM Size For Recognition                               ; Off                ; Off                ;
; Allow Any Shift Register Size For Recognition                    ; Off                ; Off                ;
; Use LogicLock Constraints during Resource Balancing              ; On                 ; On                 ;
; Ignore translate_off and synthesis_off directives                ; Off                ; Off                ;
; Timing-Driven Synthesis                                          ; On                 ; On                 ;
; Report Parameter Settings                                        ; On                 ; On                 ;
; Report Source Assignments                                        ; On                 ; On                 ;
; Report Connectivity Checks                                       ; On                 ; On                 ;
; Ignore Maximum Fan-Out Assignments                               ; Off                ; Off                ;
; Synchronization Register Chain Length                            ; 2                  ; 2                  ;
; Power Optimization During Synthesis                              ; Normal compilation ; Normal compilation ;
; HDL message level                                                ; Level2             ; Level2             ;
; Suppress Register Optimization Related Messages                  ; Off                ; Off                ;
; Number of Removed Registers Reported in Synthesis Report         ; 5000               ; 5000               ;
; Number of Swept Nodes Reported in Synthesis Report               ; 5000               ; 5000               ;
; Number of Inverted Registers Reported in Synthesis Report        ; 100                ; 100                ;
; Clock MUX Protection                                             ; On                 ; On                 ;
; Auto Gated Clock Conversion                                      ; Off                ; Off                ;
; Block Design Naming                                              ; Auto               ; Auto               ;
; SDC constraint protection                                        ; Off                ; Off                ;
; Synthesis Effort                                                 ; Auto               ; Auto               ;
; Shift Register Replacement - Allow Asynchronous Clear Signal     ; On                 ; On                 ;
; Pre-Mapping Resynthesis Optimization                             ; Off                ; Off                ;
; Analysis & Synthesis Message Level                               ; Medium             ; Medium             ;
; Disable Register Merging Across Hierarchies                      ; Auto               ; Auto               ;
; Resource Aware Inference For Block RAM                           ; On                 ; On                 ;
+------------------------------------------------------------------+--------------------+--------------------+


+------------------------------------------+
; Parallel Compilation                     ;
+----------------------------+-------------+
; Processors                 ; Number      ;
+----------------------------+-------------+
; Number detected on machine ; 20          ;
; Maximum allowed            ; 14          ;
;                            ;             ;
; Average used               ; 1.01        ;
; Maximum used               ; 14          ;
;                            ;             ;
; Usage by Processor         ; % Time Used ;
;     Processor 1            ; 100.0%      ;
;     Processor 2            ;   0.1%      ;
;     Processor 3            ;   0.0%      ;
;     Processor 4            ;   0.0%      ;
;     Processor 5            ;   0.0%      ;
;     Processor 6            ;   0.0%      ;
;     Processors 7-14        ;   0.0%      ;
+----------------------------+-------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis Source Files Read                                                                                                                                                                                                             ;
+----------------------------------------------------------------------------------+-----------------+----------------------------------------+--------------------------------------------------------------------------------------------+---------+
; File Name with User-Entered Path                                                 ; Used in Netlist ; File Type                              ; File Name with Absolute Path                                                               ; Library ;
+----------------------------------------------------------------------------------+-----------------+----------------------------------------+--------------------------------------------------------------------------------------------+---------+
; ../src/voltage_scaler_clocked_fast.v                                             ; yes             ; User Verilog HDL File                  ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/voltage_scaler_clocked_fast.v ;         ;
; ../ip/TYFIFO/TYFIFO.v                                                            ; yes             ; User Wizard-Generated File             ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/TYFIFO/TYFIFO.v                ;         ;
; ../ip/triangle_rom/triangle_rom.v                                                ; yes             ; User Wizard-Generated File             ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/triangle_rom/triangle_rom.v    ;         ;
; ../ip/sqaure_rom/sqaure_rom.v                                                    ; yes             ; User Wizard-Generated File             ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/sqaure_rom/sqaure_rom.v        ;         ;
; ../ip/sawtooth_rom/sawtooth_rom.v                                                ; yes             ; User Wizard-Generated File             ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/sawtooth_rom/sawtooth_rom.v    ;         ;
; ../ip/sin_rom/sin_rom.v                                                          ; yes             ; User Wizard-Generated File             ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/sin_rom/sin_rom.v              ;         ;
; ../ip/MYPLL/MYPLL.v                                                              ; yes             ; User Wizard-Generated File             ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/MYPLL/MYPLL.v                  ;         ;
; ../src/VOLTAGE_SCALER_CLOCKED.v                                                  ; yes             ; User Verilog HDL File                  ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/VOLTAGE_SCALER_CLOCKED.v      ;         ;
; ../src/test.v                                                                    ; yes             ; User Verilog HDL File                  ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/test.v                        ;         ;
; ../src/MASTER_CTRL.v                                                             ; yes             ; User Verilog HDL File                  ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/MASTER_CTRL.v                 ;         ;
; ../src/FREQ_DEV.v                                                                ; yes             ; User Verilog HDL File                  ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/FREQ_DEV.v                    ;         ;
; ../src/FMC_CONTROL.v                                                             ; yes             ; User Verilog HDL File                  ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/FMC_CONTROL.v                 ;         ;
; ../src/DA_WAVEFORM.v                                                             ; yes             ; User Verilog HDL File                  ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_WAVEFORM.v                 ;         ;
; ../src/DA_PARAMETER_DEAL.v                                                       ; yes             ; User Verilog HDL File                  ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v           ;         ;
; ../src/DA_PARAMETER_CTRL.v                                                       ; yes             ; User Verilog HDL File                  ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_CTRL.v           ;         ;
; ../src/CNT32.v                                                                   ; yes             ; User Verilog HDL File                  ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/CNT32.v                       ;         ;
; ../src/AD_FREQ_WORD.v                                                            ; yes             ; User Verilog HDL File                  ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v                ;         ;
; ../src/AD_FREQ_MEASURE.v                                                         ; yes             ; User Verilog HDL File                  ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v             ;         ;
; ../src/AD_DATA_DEAL.v                                                            ; yes             ; User Verilog HDL File                  ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v                ;         ;
; TOP.bdf                                                                          ; yes             ; User Block Diagram/Schematic File      ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/TOP.bdf                       ;         ;
; altpll.tdf                                                                       ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/altpll.tdf                                  ;         ;
; aglobal181.inc                                                                   ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/aglobal181.inc                              ;         ;
; stratix_pll.inc                                                                  ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/stratix_pll.inc                             ;         ;
; stratixii_pll.inc                                                                ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/stratixii_pll.inc                           ;         ;
; cycloneii_pll.inc                                                                ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/cycloneii_pll.inc                           ;         ;
; db/mypll_altpll1.v                                                               ; yes             ; Auto-Generated Megafunction            ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/mypll_altpll1.v            ;         ;
; dcfifo.tdf                                                                       ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/dcfifo.tdf                                  ;         ;
; lpm_counter.inc                                                                  ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/lpm_counter.inc                             ;         ;
; lpm_add_sub.inc                                                                  ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/lpm_add_sub.inc                             ;         ;
; altdpram.inc                                                                     ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/altdpram.inc                                ;         ;
; a_graycounter.inc                                                                ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/a_graycounter.inc                           ;         ;
; a_fefifo.inc                                                                     ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/a_fefifo.inc                                ;         ;
; a_gray2bin.inc                                                                   ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/a_gray2bin.inc                              ;         ;
; dffpipe.inc                                                                      ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/dffpipe.inc                                 ;         ;
; alt_sync_fifo.inc                                                                ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/alt_sync_fifo.inc                           ;         ;
; lpm_compare.inc                                                                  ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/lpm_compare.inc                             ;         ;
; altsyncram_fifo.inc                                                              ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/altsyncram_fifo.inc                         ;         ;
; db/dcfifo_vve1.tdf                                                               ; yes             ; Auto-Generated Megafunction            ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/dcfifo_vve1.tdf            ;         ;
; db/a_graycounter_4p6.tdf                                                         ; yes             ; Auto-Generated Megafunction            ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/a_graycounter_4p6.tdf      ;         ;
; db/a_graycounter_07c.tdf                                                         ; yes             ; Auto-Generated Megafunction            ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/a_graycounter_07c.tdf      ;         ;
; db/altsyncram_ce41.tdf                                                           ; yes             ; Auto-Generated Megafunction            ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/altsyncram_ce41.tdf        ;         ;
; db/alt_synch_pipe_qal.tdf                                                        ; yes             ; Auto-Generated Megafunction            ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/alt_synch_pipe_qal.tdf     ;         ;
; db/dffpipe_b09.tdf                                                               ; yes             ; Auto-Generated Megafunction            ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/dffpipe_b09.tdf            ;         ;
; db/alt_synch_pipe_ral.tdf                                                        ; yes             ; Auto-Generated Megafunction            ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/alt_synch_pipe_ral.tdf     ;         ;
; db/dffpipe_c09.tdf                                                               ; yes             ; Auto-Generated Megafunction            ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/dffpipe_c09.tdf            ;         ;
; db/cmpr_o76.tdf                                                                  ; yes             ; Auto-Generated Megafunction            ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/cmpr_o76.tdf               ;         ;
; altsyncram.tdf                                                                   ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/altsyncram.tdf                              ;         ;
; stratix_ram_block.inc                                                            ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/stratix_ram_block.inc                       ;         ;
; lpm_mux.inc                                                                      ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/lpm_mux.inc                                 ;         ;
; lpm_decode.inc                                                                   ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/lpm_decode.inc                              ;         ;
; a_rdenreg.inc                                                                    ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/a_rdenreg.inc                               ;         ;
; altrom.inc                                                                       ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/altrom.inc                                  ;         ;
; altram.inc                                                                       ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/altram.inc                                  ;         ;
; db/altsyncram_hva1.tdf                                                           ; yes             ; Auto-Generated Megafunction            ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/altsyncram_hva1.tdf        ;         ;
; /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/script/sine_64x14.mif     ; yes             ; Auto-Found Memory Initialization File  ; /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/script/sine_64x14.mif               ;         ;
; db/altsyncram_j6b1.tdf                                                           ; yes             ; Auto-Generated Megafunction            ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/altsyncram_j6b1.tdf        ;         ;
; /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/script/square_64x14.mif   ; yes             ; Auto-Found Memory Initialization File  ; /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/script/square_64x14.mif             ;         ;
; db/altsyncram_ocb1.tdf                                                           ; yes             ; Auto-Generated Megafunction            ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/altsyncram_ocb1.tdf        ;         ;
; /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/script/triangle_64x14.mif ; yes             ; Auto-Found Memory Initialization File  ; /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/script/triangle_64x14.mif           ;         ;
; db/altsyncram_rdb1.tdf                                                           ; yes             ; Auto-Generated Megafunction            ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/altsyncram_rdb1.tdf        ;         ;
; /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/script/sawtooth_64x14.mif ; yes             ; Auto-Found Memory Initialization File  ; /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/script/sawtooth_64x14.mif           ;         ;
; lpm_mult.tdf                                                                     ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/lpm_mult.tdf                                ;         ;
; multcore.inc                                                                     ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/multcore.inc                                ;         ;
; bypassff.inc                                                                     ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/bypassff.inc                                ;         ;
; altshift.inc                                                                     ; yes             ; Megafunction                           ; g:/altera/18.1/quartus/libraries/megafunctions/altshift.inc                                ;         ;
; db/mult_cft.tdf                                                                  ; yes             ; Auto-Generated Megafunction            ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/mult_cft.tdf               ;         ;
; db/mult_4dt.tdf                                                                  ; yes             ; Auto-Generated Megafunction            ; C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/mult_4dt.tdf               ;         ;
+----------------------------------------------------------------------------------+-----------------+----------------------------------------+--------------------------------------------------------------------------------------------+---------+


+---------------------------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis Resource Usage Summary                                                                                     ;
+---------------------------------------------+-----------------------------------------------------------------------------------+
; Resource                                    ; Usage                                                                             ;
+---------------------------------------------+-----------------------------------------------------------------------------------+
; Estimated Total logic elements              ; 1,833                                                                             ;
;                                             ;                                                                                   ;
; Total combinational functions               ; 1331                                                                              ;
; Logic element usage by number of LUT inputs ;                                                                                   ;
;     -- 4 input functions                    ; 352                                                                               ;
;     -- 3 input functions                    ; 671                                                                               ;
;     -- <=2 input functions                  ; 308                                                                               ;
;                                             ;                                                                                   ;
; Logic elements by mode                      ;                                                                                   ;
;     -- normal mode                          ; 902                                                                               ;
;     -- arithmetic mode                      ; 429                                                                               ;
;                                             ;                                                                                   ;
; Total registers                             ; 1106                                                                              ;
;     -- Dedicated logic registers            ; 1106                                                                              ;
;     -- I/O registers                        ; 0                                                                                 ;
;                                             ;                                                                                   ;
; I/O pins                                    ; 80                                                                                ;
; Total memory bits                           ; 31744                                                                             ;
;                                             ;                                                                                   ;
; Embedded Multiplier 9-bit elements          ; 10                                                                                ;
;                                             ;                                                                                   ;
; Total PLLs                                  ; 1                                                                                 ;
;     -- PLLs                                 ; 1                                                                                 ;
;                                             ;                                                                                   ;
; Maximum fan-out node                        ; MYPLL:inst6|altpll:altpll_component|MYPLL_altpll1:auto_generated|wire_pll1_clk[0] ;
; Maximum fan-out                             ; 559                                                                               ;
; Total fan-out                               ; 8924                                                                              ;
; Average fan-out                             ; 3.23                                                                              ;
+---------------------------------------------+-----------------------------------------------------------------------------------+


+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis Resource Utilization by Entity                                                                                                                                                                                                                                                                                               ;
+----------------------------------------------+---------------------+---------------------------+-------------+--------------+---------+-----------+------+--------------+----------------------------------------------------------------------------------------------------------------------------+-----------------------------+--------------+
; Compilation Hierarchy Node                   ; Combinational ALUTs ; Dedicated Logic Registers ; Memory Bits ; DSP Elements ; DSP 9x9 ; DSP 18x18 ; Pins ; Virtual Pins ; Full Hierarchy Name                                                                                                        ; Entity Name                 ; Library Name ;
+----------------------------------------------+---------------------+---------------------------+-------------+--------------+---------+-----------+------+--------------+----------------------------------------------------------------------------------------------------------------------------+-----------------------------+--------------+
; |TOP                                         ; 1331 (0)            ; 1106 (2)                  ; 31744       ; 10           ; 2       ; 4         ; 80   ; 0            ; |TOP                                                                                                                       ; TOP                         ; work         ;
;    |AD_DATA_DEAL:u_AD_DATA_DEAL|             ; 30 (30)             ; 0 (0)                     ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|AD_DATA_DEAL:u_AD_DATA_DEAL                                                                                           ; AD_DATA_DEAL                ; work         ;
;    |AD_FREQ_MEASURE:u_AD_FREQ_MEASURE|       ; 137 (137)           ; 0 (0)                     ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE                                                                                     ; AD_FREQ_MEASURE             ; work         ;
;    |AD_FREQ_WORD:u_AD_FREQ_WORD|             ; 68 (68)             ; 0 (0)                     ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|AD_FREQ_WORD:u_AD_FREQ_WORD                                                                                           ; AD_FREQ_WORD                ; work         ;
;    |CNT32:u_AD1_CNT32|                       ; 64 (64)             ; 64 (64)                   ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|CNT32:u_AD1_CNT32                                                                                                     ; CNT32                       ; work         ;
;    |CNT32:u_AD2_CNT32|                       ; 64 (64)             ; 64 (64)                   ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|CNT32:u_AD2_CNT32                                                                                                     ; CNT32                       ; work         ;
;    |DA_PARAMETER_CTRL:inst7|                 ; 243 (243)           ; 156 (156)                 ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_PARAMETER_CTRL:inst7                                                                                               ; DA_PARAMETER_CTRL           ; work         ;
;    |DA_PARAMETER_DEAL:inst1|                 ; 112 (112)           ; 0 (0)                     ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_PARAMETER_DEAL:inst1                                                                                               ; DA_PARAMETER_DEAL           ; work         ;
;    |DA_WAVEFORM:inst5|                       ; 33 (33)             ; 28 (28)                   ; 3584        ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_WAVEFORM:inst5                                                                                                     ; DA_WAVEFORM                 ; work         ;
;       |sawtooth_rom:sawtooth_rom_inst|       ; 0 (0)               ; 0 (0)                     ; 896         ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_WAVEFORM:inst5|sawtooth_rom:sawtooth_rom_inst                                                                      ; sawtooth_rom                ; work         ;
;          |altsyncram:altsyncram_component|   ; 0 (0)               ; 0 (0)                     ; 896         ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_WAVEFORM:inst5|sawtooth_rom:sawtooth_rom_inst|altsyncram:altsyncram_component                                      ; altsyncram                  ; work         ;
;             |altsyncram_rdb1:auto_generated| ; 0 (0)               ; 0 (0)                     ; 896         ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_WAVEFORM:inst5|sawtooth_rom:sawtooth_rom_inst|altsyncram:altsyncram_component|altsyncram_rdb1:auto_generated       ; altsyncram_rdb1             ; work         ;
;       |sin_rom:sin_rom_inst|                 ; 0 (0)               ; 0 (0)                     ; 896         ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_WAVEFORM:inst5|sin_rom:sin_rom_inst                                                                                ; sin_rom                     ; work         ;
;          |altsyncram:altsyncram_component|   ; 0 (0)               ; 0 (0)                     ; 896         ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_WAVEFORM:inst5|sin_rom:sin_rom_inst|altsyncram:altsyncram_component                                                ; altsyncram                  ; work         ;
;             |altsyncram_hva1:auto_generated| ; 0 (0)               ; 0 (0)                     ; 896         ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_WAVEFORM:inst5|sin_rom:sin_rom_inst|altsyncram:altsyncram_component|altsyncram_hva1:auto_generated                 ; altsyncram_hva1             ; work         ;
;       |sqaure_rom:sqaure_rom_inst|           ; 0 (0)               ; 0 (0)                     ; 896         ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_WAVEFORM:inst5|sqaure_rom:sqaure_rom_inst                                                                          ; sqaure_rom                  ; work         ;
;          |altsyncram:altsyncram_component|   ; 0 (0)               ; 0 (0)                     ; 896         ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_WAVEFORM:inst5|sqaure_rom:sqaure_rom_inst|altsyncram:altsyncram_component                                          ; altsyncram                  ; work         ;
;             |altsyncram_j6b1:auto_generated| ; 0 (0)               ; 0 (0)                     ; 896         ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_WAVEFORM:inst5|sqaure_rom:sqaure_rom_inst|altsyncram:altsyncram_component|altsyncram_j6b1:auto_generated           ; altsyncram_j6b1             ; work         ;
;       |triangle_rom:triangle_rom_inst|       ; 0 (0)               ; 0 (0)                     ; 896         ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_WAVEFORM:inst5|triangle_rom:triangle_rom_inst                                                                      ; triangle_rom                ; work         ;
;          |altsyncram:altsyncram_component|   ; 0 (0)               ; 0 (0)                     ; 896         ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_WAVEFORM:inst5|triangle_rom:triangle_rom_inst|altsyncram:altsyncram_component                                      ; altsyncram                  ; work         ;
;             |altsyncram_ocb1:auto_generated| ; 0 (0)               ; 0 (0)                     ; 896         ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_WAVEFORM:inst5|triangle_rom:triangle_rom_inst|altsyncram:altsyncram_component|altsyncram_ocb1:auto_generated       ; altsyncram_ocb1             ; work         ;
;    |DA_WAVEFORM:inst8|                       ; 33 (33)             ; 28 (28)                   ; 3584        ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_WAVEFORM:inst8                                                                                                     ; DA_WAVEFORM                 ; work         ;
;       |sawtooth_rom:sawtooth_rom_inst|       ; 0 (0)               ; 0 (0)                     ; 896         ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_WAVEFORM:inst8|sawtooth_rom:sawtooth_rom_inst                                                                      ; sawtooth_rom                ; work         ;
;          |altsyncram:altsyncram_component|   ; 0 (0)               ; 0 (0)                     ; 896         ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_WAVEFORM:inst8|sawtooth_rom:sawtooth_rom_inst|altsyncram:altsyncram_component                                      ; altsyncram                  ; work         ;
;             |altsyncram_rdb1:auto_generated| ; 0 (0)               ; 0 (0)                     ; 896         ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_WAVEFORM:inst8|sawtooth_rom:sawtooth_rom_inst|altsyncram:altsyncram_component|altsyncram_rdb1:auto_generated       ; altsyncram_rdb1             ; work         ;
;       |sin_rom:sin_rom_inst|                 ; 0 (0)               ; 0 (0)                     ; 896         ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_WAVEFORM:inst8|sin_rom:sin_rom_inst                                                                                ; sin_rom                     ; work         ;
;          |altsyncram:altsyncram_component|   ; 0 (0)               ; 0 (0)                     ; 896         ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_WAVEFORM:inst8|sin_rom:sin_rom_inst|altsyncram:altsyncram_component                                                ; altsyncram                  ; work         ;
;             |altsyncram_hva1:auto_generated| ; 0 (0)               ; 0 (0)                     ; 896         ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_WAVEFORM:inst8|sin_rom:sin_rom_inst|altsyncram:altsyncram_component|altsyncram_hva1:auto_generated                 ; altsyncram_hva1             ; work         ;
;       |sqaure_rom:sqaure_rom_inst|           ; 0 (0)               ; 0 (0)                     ; 896         ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_WAVEFORM:inst8|sqaure_rom:sqaure_rom_inst                                                                          ; sqaure_rom                  ; work         ;
;          |altsyncram:altsyncram_component|   ; 0 (0)               ; 0 (0)                     ; 896         ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_WAVEFORM:inst8|sqaure_rom:sqaure_rom_inst|altsyncram:altsyncram_component                                          ; altsyncram                  ; work         ;
;             |altsyncram_j6b1:auto_generated| ; 0 (0)               ; 0 (0)                     ; 896         ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_WAVEFORM:inst8|sqaure_rom:sqaure_rom_inst|altsyncram:altsyncram_component|altsyncram_j6b1:auto_generated           ; altsyncram_j6b1             ; work         ;
;       |triangle_rom:triangle_rom_inst|       ; 0 (0)               ; 0 (0)                     ; 896         ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_WAVEFORM:inst8|triangle_rom:triangle_rom_inst                                                                      ; triangle_rom                ; work         ;
;          |altsyncram:altsyncram_component|   ; 0 (0)               ; 0 (0)                     ; 896         ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_WAVEFORM:inst8|triangle_rom:triangle_rom_inst|altsyncram:altsyncram_component                                      ; altsyncram                  ; work         ;
;             |altsyncram_ocb1:auto_generated| ; 0 (0)               ; 0 (0)                     ; 896         ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|DA_WAVEFORM:inst8|triangle_rom:triangle_rom_inst|altsyncram:altsyncram_component|altsyncram_ocb1:auto_generated       ; altsyncram_ocb1             ; work         ;
;    |FMC_CONTROL:inst|                        ; 188 (188)           ; 232 (232)                 ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|FMC_CONTROL:inst                                                                                                      ; FMC_CONTROL                 ; work         ;
;    |FREQ_DEV:u_AD1_DEV|                      ; 32 (32)             ; 65 (65)                   ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|FREQ_DEV:u_AD1_DEV                                                                                                    ; FREQ_DEV                    ; work         ;
;    |FREQ_DEV:u_AD2_DEV|                      ; 32 (32)             ; 65 (65)                   ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|FREQ_DEV:u_AD2_DEV                                                                                                    ; FREQ_DEV                    ; work         ;
;    |MASTER_CTRL:inst3|                       ; 17 (17)             ; 0 (0)                     ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|MASTER_CTRL:inst3                                                                                                     ; MASTER_CTRL                 ; work         ;
;    |MYPLL:inst6|                             ; 0 (0)               ; 0 (0)                     ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|MYPLL:inst6                                                                                                           ; MYPLL                       ; work         ;
;       |altpll:altpll_component|              ; 0 (0)               ; 0 (0)                     ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|MYPLL:inst6|altpll:altpll_component                                                                                   ; altpll                      ; work         ;
;          |MYPLL_altpll1:auto_generated|      ; 0 (0)               ; 0 (0)                     ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|MYPLL:inst6|altpll:altpll_component|MYPLL_altpll1:auto_generated                                                      ; MYPLL_altpll1               ; work         ;
;    |TYFIFO:u_AD1_FIFO|                       ; 66 (0)              ; 107 (0)                   ; 12288       ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|TYFIFO:u_AD1_FIFO                                                                                                     ; TYFIFO                      ; work         ;
;       |dcfifo:dcfifo_component|              ; 66 (0)              ; 107 (0)                   ; 12288       ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component                                                                             ; dcfifo                      ; work         ;
;          |dcfifo_vve1:auto_generated|        ; 66 (5)              ; 107 (33)                  ; 12288       ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated                                                  ; dcfifo_vve1                 ; work         ;
;             |a_graycounter_07c:wrptr_g1p|    ; 22 (22)             ; 15 (15)                   ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|a_graycounter_07c:wrptr_g1p                      ; a_graycounter_07c           ; work         ;
;             |a_graycounter_4p6:rdptr_g1p|    ; 24 (24)             ; 15 (15)                   ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|a_graycounter_4p6:rdptr_g1p                      ; a_graycounter_4p6           ; work         ;
;             |alt_synch_pipe_qal:rs_dgwp|     ; 0 (0)               ; 22 (0)                    ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp                       ; alt_synch_pipe_qal          ; work         ;
;                |dffpipe_b09:dffpipe12|       ; 0 (0)               ; 22 (22)                   ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12 ; dffpipe_b09                 ; work         ;
;             |alt_synch_pipe_ral:ws_dgrp|     ; 0 (0)               ; 22 (0)                    ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp                       ; alt_synch_pipe_ral          ; work         ;
;                |dffpipe_c09:dffpipe15|       ; 0 (0)               ; 22 (22)                   ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15 ; dffpipe_c09                 ; work         ;
;             |altsyncram_ce41:fifo_ram|       ; 0 (0)               ; 0 (0)                     ; 12288       ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|altsyncram_ce41:fifo_ram                         ; altsyncram_ce41             ; work         ;
;             |cmpr_o76:rdempty_eq_comp|       ; 7 (7)               ; 0 (0)                     ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|cmpr_o76:rdempty_eq_comp                         ; cmpr_o76                    ; work         ;
;             |cmpr_o76:wrfull_eq_comp|        ; 8 (8)               ; 0 (0)                     ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|cmpr_o76:wrfull_eq_comp                          ; cmpr_o76                    ; work         ;
;    |TYFIFO:u_AD2_FIFO|                       ; 66 (0)              ; 107 (0)                   ; 12288       ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|TYFIFO:u_AD2_FIFO                                                                                                     ; TYFIFO                      ; work         ;
;       |dcfifo:dcfifo_component|              ; 66 (0)              ; 107 (0)                   ; 12288       ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component                                                                             ; dcfifo                      ; work         ;
;          |dcfifo_vve1:auto_generated|        ; 66 (5)              ; 107 (33)                  ; 12288       ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated                                                  ; dcfifo_vve1                 ; work         ;
;             |a_graycounter_07c:wrptr_g1p|    ; 22 (22)             ; 15 (15)                   ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|a_graycounter_07c:wrptr_g1p                      ; a_graycounter_07c           ; work         ;
;             |a_graycounter_4p6:rdptr_g1p|    ; 24 (24)             ; 15 (15)                   ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|a_graycounter_4p6:rdptr_g1p                      ; a_graycounter_4p6           ; work         ;
;             |alt_synch_pipe_qal:rs_dgwp|     ; 0 (0)               ; 22 (0)                    ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp                       ; alt_synch_pipe_qal          ; work         ;
;                |dffpipe_b09:dffpipe12|       ; 0 (0)               ; 22 (22)                   ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12 ; dffpipe_b09                 ; work         ;
;             |alt_synch_pipe_ral:ws_dgrp|     ; 0 (0)               ; 22 (0)                    ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp                       ; alt_synch_pipe_ral          ; work         ;
;                |dffpipe_c09:dffpipe15|       ; 0 (0)               ; 22 (22)                   ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15 ; dffpipe_c09                 ; work         ;
;             |altsyncram_ce41:fifo_ram|       ; 0 (0)               ; 0 (0)                     ; 12288       ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|altsyncram_ce41:fifo_ram                         ; altsyncram_ce41             ; work         ;
;             |cmpr_o76:rdempty_eq_comp|       ; 7 (7)               ; 0 (0)                     ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|cmpr_o76:rdempty_eq_comp                         ; cmpr_o76                    ; work         ;
;             |cmpr_o76:wrfull_eq_comp|        ; 8 (8)               ; 0 (0)                     ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |TOP|TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|cmpr_o76:wrfull_eq_comp                          ; cmpr_o76                    ; work         ;
;    |voltage_scaler_clocked_fast:inst10|      ; 73 (46)             ; 94 (94)                   ; 0           ; 5            ; 1       ; 2         ; 0    ; 0            ; |TOP|voltage_scaler_clocked_fast:inst10                                                                                    ; voltage_scaler_clocked_fast ; work         ;
;       |lpm_mult:Mult0|                       ; 0 (0)               ; 0 (0)                     ; 0           ; 2            ; 0       ; 1         ; 0    ; 0            ; |TOP|voltage_scaler_clocked_fast:inst10|lpm_mult:Mult0                                                                     ; lpm_mult                    ; work         ;
;          |mult_4dt:auto_generated|           ; 0 (0)               ; 0 (0)                     ; 0           ; 2            ; 0       ; 1         ; 0    ; 0            ; |TOP|voltage_scaler_clocked_fast:inst10|lpm_mult:Mult0|mult_4dt:auto_generated                                             ; mult_4dt                    ; work         ;
;       |lpm_mult:Mult1|                       ; 27 (0)              ; 0 (0)                     ; 0           ; 3            ; 1       ; 1         ; 0    ; 0            ; |TOP|voltage_scaler_clocked_fast:inst10|lpm_mult:Mult1                                                                     ; lpm_mult                    ; work         ;
;          |mult_cft:auto_generated|           ; 27 (27)             ; 0 (0)                     ; 0           ; 3            ; 1       ; 1         ; 0    ; 0            ; |TOP|voltage_scaler_clocked_fast:inst10|lpm_mult:Mult1|mult_cft:auto_generated                                             ; mult_cft                    ; work         ;
;    |voltage_scaler_clocked_fast:inst14|      ; 73 (46)             ; 94 (94)                   ; 0           ; 5            ; 1       ; 2         ; 0    ; 0            ; |TOP|voltage_scaler_clocked_fast:inst14                                                                                    ; voltage_scaler_clocked_fast ; work         ;
;       |lpm_mult:Mult0|                       ; 0 (0)               ; 0 (0)                     ; 0           ; 2            ; 0       ; 1         ; 0    ; 0            ; |TOP|voltage_scaler_clocked_fast:inst14|lpm_mult:Mult0                                                                     ; lpm_mult                    ; work         ;
;          |mult_4dt:auto_generated|           ; 0 (0)               ; 0 (0)                     ; 0           ; 2            ; 0       ; 1         ; 0    ; 0            ; |TOP|voltage_scaler_clocked_fast:inst14|lpm_mult:Mult0|mult_4dt:auto_generated                                             ; mult_4dt                    ; work         ;
;       |lpm_mult:Mult1|                       ; 27 (0)              ; 0 (0)                     ; 0           ; 3            ; 1       ; 1         ; 0    ; 0            ; |TOP|voltage_scaler_clocked_fast:inst14|lpm_mult:Mult1                                                                     ; lpm_mult                    ; work         ;
;          |mult_cft:auto_generated|           ; 27 (27)             ; 0 (0)                     ; 0           ; 3            ; 1       ; 1         ; 0    ; 0            ; |TOP|voltage_scaler_clocked_fast:inst14|lpm_mult:Mult1|mult_cft:auto_generated                                             ; mult_cft                    ; work         ;
+----------------------------------------------+---------------------+---------------------------+-------------+--------------+---------+-----------+------+--------------+----------------------------------------------------------------------------------------------------------------------------+-----------------------------+--------------+
Note: For table entries with two numbers listed, the numbers in parentheses indicate the number of resources of the given type used by the specific entity alone. The numbers listed outside of parentheses indicate the total resources of the given type used by the specific entity and all of its sub-entities in the hierarchy.


+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis RAM Summary                                                                                                                                                                                                                        ;
+----------------------------------------------------------------------------------------------------------------------------+------+------------------+--------------+--------------+--------------+--------------+-------+------------------------------+
; Name                                                                                                                       ; Type ; Mode             ; Port A Depth ; Port A Width ; Port B Depth ; Port B Width ; Size  ; MIF                          ;
+----------------------------------------------------------------------------------------------------------------------------+------+------------------+--------------+--------------+--------------+--------------+-------+------------------------------+
; DA_WAVEFORM:inst5|sawtooth_rom:sawtooth_rom_inst|altsyncram:altsyncram_component|altsyncram_rdb1:auto_generated|ALTSYNCRAM ; AUTO ; ROM              ; 64           ; 14           ; --           ; --           ; 896   ; ../script/sawtooth_64x14.mif ;
; DA_WAVEFORM:inst5|sin_rom:sin_rom_inst|altsyncram:altsyncram_component|altsyncram_hva1:auto_generated|ALTSYNCRAM           ; AUTO ; ROM              ; 64           ; 14           ; --           ; --           ; 896   ; ../script/sine_64x14.mif     ;
; DA_WAVEFORM:inst5|sqaure_rom:sqaure_rom_inst|altsyncram:altsyncram_component|altsyncram_j6b1:auto_generated|ALTSYNCRAM     ; AUTO ; ROM              ; 64           ; 14           ; --           ; --           ; 896   ; ../script/square_64x14.mif   ;
; DA_WAVEFORM:inst5|triangle_rom:triangle_rom_inst|altsyncram:altsyncram_component|altsyncram_ocb1:auto_generated|ALTSYNCRAM ; AUTO ; ROM              ; 64           ; 14           ; --           ; --           ; 896   ; ../script/triangle_64x14.mif ;
; DA_WAVEFORM:inst8|sawtooth_rom:sawtooth_rom_inst|altsyncram:altsyncram_component|altsyncram_rdb1:auto_generated|ALTSYNCRAM ; AUTO ; ROM              ; 64           ; 14           ; --           ; --           ; 896   ; ../script/sawtooth_64x14.mif ;
; DA_WAVEFORM:inst8|sin_rom:sin_rom_inst|altsyncram:altsyncram_component|altsyncram_hva1:auto_generated|ALTSYNCRAM           ; AUTO ; ROM              ; 64           ; 14           ; --           ; --           ; 896   ; ../script/sine_64x14.mif     ;
; DA_WAVEFORM:inst8|sqaure_rom:sqaure_rom_inst|altsyncram:altsyncram_component|altsyncram_j6b1:auto_generated|ALTSYNCRAM     ; AUTO ; ROM              ; 64           ; 14           ; --           ; --           ; 896   ; ../script/square_64x14.mif   ;
; DA_WAVEFORM:inst8|triangle_rom:triangle_rom_inst|altsyncram:altsyncram_component|altsyncram_ocb1:auto_generated|ALTSYNCRAM ; AUTO ; ROM              ; 64           ; 14           ; --           ; --           ; 896   ; ../script/triangle_64x14.mif ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|altsyncram_ce41:fifo_ram|ALTSYNCRAM                   ; AUTO ; Simple Dual Port ; 1024         ; 12           ; 1024         ; 12           ; 12288 ; None                         ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|altsyncram_ce41:fifo_ram|ALTSYNCRAM                   ; AUTO ; Simple Dual Port ; 1024         ; 12           ; 1024         ; 12           ; 12288 ; None                         ;
+----------------------------------------------------------------------------------------------------------------------------+------+------------------+--------------+--------------+--------------+--------------+-------+------------------------------+


+-----------------------------------------------------+
; Analysis & Synthesis DSP Block Usage Summary        ;
+---------------------------------------+-------------+
; Statistic                             ; Number Used ;
+---------------------------------------+-------------+
; Simple Multipliers (9-bit)            ; 2           ;
; Simple Multipliers (18-bit)           ; 4           ;
; Embedded Multiplier Blocks            ; --          ;
; Embedded Multiplier 9-bit elements    ; 10          ;
; Signed Embedded Multipliers           ; 0           ;
; Unsigned Embedded Multipliers         ; 6           ;
; Mixed Sign Embedded Multipliers       ; 0           ;
; Variable Sign Embedded Multipliers    ; 0           ;
; Dedicated Input Shift Register Chains ; 0           ;
+---------------------------------------+-------------+
Note: number of Embedded Multiplier Blocks used is only available after a successful fit.


+-----------------------------------------------------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis IP Cores Summary                                                                                                                     ;
+--------+--------------+---------+--------------+--------------+-------------------------------------------------------+-----------------------------------+
; Vendor ; IP Core Name ; Version ; Release Date ; License Type ; Entity Instance                                       ; IP Include File                   ;
+--------+--------------+---------+--------------+--------------+-------------------------------------------------------+-----------------------------------+
; Altera ; ROM: 1-PORT  ; 18.1    ; N/A          ; N/A          ; |TOP|DA_WAVEFORM:inst5|sawtooth_rom:sawtooth_rom_inst ; ../ip/sawtooth_rom/sawtooth_rom.v ;
; Altera ; ROM: 1-PORT  ; 18.1    ; N/A          ; N/A          ; |TOP|DA_WAVEFORM:inst5|sin_rom:sin_rom_inst           ; ../ip/sin_rom/sin_rom.v           ;
; Altera ; ROM: 1-PORT  ; 18.1    ; N/A          ; N/A          ; |TOP|DA_WAVEFORM:inst5|sqaure_rom:sqaure_rom_inst     ; ../ip/sqaure_rom/sqaure_rom.v     ;
; Altera ; ROM: 1-PORT  ; 18.1    ; N/A          ; N/A          ; |TOP|DA_WAVEFORM:inst5|triangle_rom:triangle_rom_inst ; ../ip/triangle_rom/triangle_rom.v ;
; Altera ; ALTPLL       ; 18.1    ; N/A          ; N/A          ; |TOP|MYPLL:inst6                                      ; ../ip/MYPLL/MYPLL.v               ;
; Altera ; ROM: 1-PORT  ; 18.1    ; N/A          ; N/A          ; |TOP|DA_WAVEFORM:inst8|sawtooth_rom:sawtooth_rom_inst ; ../ip/sawtooth_rom/sawtooth_rom.v ;
; Altera ; ROM: 1-PORT  ; 18.1    ; N/A          ; N/A          ; |TOP|DA_WAVEFORM:inst8|sin_rom:sin_rom_inst           ; ../ip/sin_rom/sin_rom.v           ;
; Altera ; ROM: 1-PORT  ; 18.1    ; N/A          ; N/A          ; |TOP|DA_WAVEFORM:inst8|sqaure_rom:sqaure_rom_inst     ; ../ip/sqaure_rom/sqaure_rom.v     ;
; Altera ; ROM: 1-PORT  ; 18.1    ; N/A          ; N/A          ; |TOP|DA_WAVEFORM:inst8|triangle_rom:triangle_rom_inst ; ../ip/triangle_rom/triangle_rom.v ;
; Altera ; FIFO         ; 18.1    ; N/A          ; N/A          ; |TOP|TYFIFO:u_AD1_FIFO                                ; ../ip/TYFIFO/TYFIFO.v             ;
; Altera ; FIFO         ; 18.1    ; N/A          ; N/A          ; |TOP|TYFIFO:u_AD2_FIFO                                ; ../ip/TYFIFO/TYFIFO.v             ;
+--------+--------------+---------+--------------+--------------+-------------------------------------------------------+-----------------------------------+


+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Registers Protected by Synthesis                                                                                                                                                                                                                  ;
+-----------------------------------------------------------------------------------------------------------------------------------+------------------------------------------------------------------+--------------------------------------------+
; Register Name                                                                                                                     ; Protected by Synthesis Attribute or Preserve Register Assignment ; Not to be Touched by Netlist Optimizations ;
+-----------------------------------------------------------------------------------------------------------------------------------+------------------------------------------------------------------+--------------------------------------------+
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe17a[10] ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe17a[8]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe17a[9]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe17a[6]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe17a[7]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe17a[4]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe17a[5]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe17a[2]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe17a[3]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe17a[0]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe17a[1]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe17a[10] ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe17a[8]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe17a[9]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe17a[6]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe17a[7]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe17a[4]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe17a[5]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe17a[2]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe17a[3]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe17a[0]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe17a[1]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe14a[10] ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe14a[8]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe14a[9]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe14a[6]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe14a[7]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe14a[4]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe14a[5]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe14a[2]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe14a[3]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe14a[0]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe14a[1]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe14a[10] ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe14a[8]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe14a[9]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe14a[6]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe14a[7]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe14a[4]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe14a[5]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe14a[2]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe14a[3]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe14a[0]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe14a[1]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe16a[10] ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe16a[8]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe16a[9]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe16a[6]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe16a[7]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe16a[4]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe16a[5]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe16a[2]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe16a[3]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe16a[0]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe16a[1]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe16a[10] ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe16a[8]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe16a[9]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe16a[6]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe16a[7]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe16a[4]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe16a[5]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe16a[2]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe16a[3]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe16a[0]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15|dffe16a[1]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe13a[10] ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe13a[8]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe13a[9]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe13a[6]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe13a[7]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe13a[4]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe13a[5]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe13a[2]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe13a[3]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe13a[0]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe13a[1]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe13a[10] ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe13a[8]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe13a[9]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe13a[6]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe13a[7]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe13a[4]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe13a[5]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe13a[2]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe13a[3]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe13a[0]  ; yes                                                              ; yes                                        ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12|dffe13a[1]  ; yes                                                              ; yes                                        ;
; Total number of protected registers is 88                                                                                         ;                                                                  ;                                            ;
+-----------------------------------------------------------------------------------------------------------------------------------+------------------------------------------------------------------+--------------------------------------------+


+--------------------------------------------------------------------------------------------------------------------------+
; User-Specified and Inferred Latches                                                                                      ;
+------------------------------------------------------+------------------------------------------+------------------------+
; Latch Name                                           ; Latch Enable Signal                      ; Free of Timing Hazards ;
+------------------------------------------------------+------------------------------------------+------------------------+
; MASTER_CTRL:inst3|CTRL_DATA[0]                       ; MASTER_CTRL:inst3|always0                ; yes                    ;
; FMC_CONTROL:inst|addr[1]                             ; FMC_CONTROL:inst|addr                    ; yes                    ;
; FMC_CONTROL:inst|addr[2]                             ; FMC_CONTROL:inst|addr                    ; yes                    ;
; FMC_CONTROL:inst|addr[3]                             ; FMC_CONTROL:inst|addr                    ; yes                    ;
; FMC_CONTROL:inst|addr[4]                             ; FMC_CONTROL:inst|addr                    ; yes                    ;
; FMC_CONTROL:inst|addr[5]                             ; FMC_CONTROL:inst|addr                    ; yes                    ;
; FMC_CONTROL:inst|addr[6]                             ; FMC_CONTROL:inst|addr                    ; yes                    ;
; FMC_CONTROL:inst|addr[7]                             ; FMC_CONTROL:inst|addr                    ; yes                    ;
; FMC_CONTROL:inst|addr[8]                             ; FMC_CONTROL:inst|addr                    ; yes                    ;
; FMC_CONTROL:inst|addr[9]                             ; FMC_CONTROL:inst|addr                    ; yes                    ;
; FMC_CONTROL:inst|addr[10]                            ; FMC_CONTROL:inst|addr                    ; yes                    ;
; FMC_CONTROL:inst|addr[11]                            ; FMC_CONTROL:inst|addr                    ; yes                    ;
; FMC_CONTROL:inst|addr[12]                            ; FMC_CONTROL:inst|addr                    ; yes                    ;
; FMC_CONTROL:inst|addr[13]                            ; FMC_CONTROL:inst|addr                    ; yes                    ;
; FMC_CONTROL:inst|addr[14]                            ; FMC_CONTROL:inst|addr                    ; yes                    ;
; FMC_CONTROL:inst|addr[15]                            ; FMC_CONTROL:inst|addr                    ; yes                    ;
; FMC_CONTROL:inst|addr[0]                             ; FMC_CONTROL:inst|addr                    ; yes                    ;
; MASTER_CTRL:inst3|CTRL_DATA[2]                       ; MASTER_CTRL:inst3|always0                ; yes                    ;
; MASTER_CTRL:inst3|CTRL_DATA[3]                       ; MASTER_CTRL:inst3|always0                ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTH[15]                 ; DA_PARAMETER_DEAL:inst1|DA1_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTH[14]                 ; DA_PARAMETER_DEAL:inst1|DA1_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTH[13]                 ; DA_PARAMETER_DEAL:inst1|DA1_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTH[12]                 ; DA_PARAMETER_DEAL:inst1|DA1_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTH[11]                 ; DA_PARAMETER_DEAL:inst1|DA1_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTH[10]                 ; DA_PARAMETER_DEAL:inst1|DA1_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTH[9]                  ; DA_PARAMETER_DEAL:inst1|DA1_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTH[8]                  ; DA_PARAMETER_DEAL:inst1|DA1_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTH[7]                  ; DA_PARAMETER_DEAL:inst1|DA1_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTH[6]                  ; DA_PARAMETER_DEAL:inst1|DA1_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTH[5]                  ; DA_PARAMETER_DEAL:inst1|DA1_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTH[4]                  ; DA_PARAMETER_DEAL:inst1|DA1_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTH[3]                  ; DA_PARAMETER_DEAL:inst1|DA1_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTH[2]                  ; DA_PARAMETER_DEAL:inst1|DA1_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTH[1]                  ; DA_PARAMETER_DEAL:inst1|DA1_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTH[0]                  ; DA_PARAMETER_DEAL:inst1|DA1_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTL[15]                 ; DA_PARAMETER_DEAL:inst1|DA1_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTL[14]                 ; DA_PARAMETER_DEAL:inst1|DA1_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTL[13]                 ; DA_PARAMETER_DEAL:inst1|DA1_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTL[12]                 ; DA_PARAMETER_DEAL:inst1|DA1_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTL[11]                 ; DA_PARAMETER_DEAL:inst1|DA1_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTL[10]                 ; DA_PARAMETER_DEAL:inst1|DA1_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTL[9]                  ; DA_PARAMETER_DEAL:inst1|DA1_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTL[8]                  ; DA_PARAMETER_DEAL:inst1|DA1_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTL[7]                  ; DA_PARAMETER_DEAL:inst1|DA1_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTL[6]                  ; DA_PARAMETER_DEAL:inst1|DA1_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTL[5]                  ; DA_PARAMETER_DEAL:inst1|DA1_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTL[4]                  ; DA_PARAMETER_DEAL:inst1|DA1_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTL[3]                  ; DA_PARAMETER_DEAL:inst1|DA1_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTL[2]                  ; DA_PARAMETER_DEAL:inst1|DA1_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTL[1]                  ; DA_PARAMETER_DEAL:inst1|DA1_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA1_OUTL[0]                  ; DA_PARAMETER_DEAL:inst1|DA1_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTL[0]                  ; DA_PARAMETER_DEAL:inst1|DA2_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTL[1]                  ; DA_PARAMETER_DEAL:inst1|DA2_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTL[2]                  ; DA_PARAMETER_DEAL:inst1|DA2_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTL[3]                  ; DA_PARAMETER_DEAL:inst1|DA2_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTL[4]                  ; DA_PARAMETER_DEAL:inst1|DA2_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTL[5]                  ; DA_PARAMETER_DEAL:inst1|DA2_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTL[6]                  ; DA_PARAMETER_DEAL:inst1|DA2_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTL[7]                  ; DA_PARAMETER_DEAL:inst1|DA2_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTL[8]                  ; DA_PARAMETER_DEAL:inst1|DA2_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTL[9]                  ; DA_PARAMETER_DEAL:inst1|DA2_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTL[10]                 ; DA_PARAMETER_DEAL:inst1|DA2_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTL[11]                 ; DA_PARAMETER_DEAL:inst1|DA2_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTL[12]                 ; DA_PARAMETER_DEAL:inst1|DA2_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTL[13]                 ; DA_PARAMETER_DEAL:inst1|DA2_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTL[14]                 ; DA_PARAMETER_DEAL:inst1|DA2_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTL[15]                 ; DA_PARAMETER_DEAL:inst1|DA2_OUTL[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTH[0]                  ; DA_PARAMETER_DEAL:inst1|DA2_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTH[1]                  ; DA_PARAMETER_DEAL:inst1|DA2_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTH[2]                  ; DA_PARAMETER_DEAL:inst1|DA2_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTH[3]                  ; DA_PARAMETER_DEAL:inst1|DA2_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTH[4]                  ; DA_PARAMETER_DEAL:inst1|DA2_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTH[5]                  ; DA_PARAMETER_DEAL:inst1|DA2_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTH[6]                  ; DA_PARAMETER_DEAL:inst1|DA2_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTH[7]                  ; DA_PARAMETER_DEAL:inst1|DA2_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTH[8]                  ; DA_PARAMETER_DEAL:inst1|DA2_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTH[9]                  ; DA_PARAMETER_DEAL:inst1|DA2_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTH[10]                 ; DA_PARAMETER_DEAL:inst1|DA2_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTH[11]                 ; DA_PARAMETER_DEAL:inst1|DA2_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTH[12]                 ; DA_PARAMETER_DEAL:inst1|DA2_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTH[13]                 ; DA_PARAMETER_DEAL:inst1|DA2_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTH[14]                 ; DA_PARAMETER_DEAL:inst1|DA2_OUTH[15]     ; yes                    ;
; DA_PARAMETER_DEAL:inst1|DA2_OUTH[15]                 ; DA_PARAMETER_DEAL:inst1|DA2_OUTH[15]     ; yes                    ;
; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[15]             ; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[15] ; yes                    ;
; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[14]             ; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[15] ; yes                    ;
; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[13]             ; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[15] ; yes                    ;
; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[12]             ; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[15] ; yes                    ;
; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[11]             ; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[15] ; yes                    ;
; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[10]             ; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[15] ; yes                    ;
; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[9]              ; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[15] ; yes                    ;
; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[8]              ; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[15] ; yes                    ;
; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[7]              ; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[15] ; yes                    ;
; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[6]              ; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[15] ; yes                    ;
; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[5]              ; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[15] ; yes                    ;
; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[4]              ; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[15] ; yes                    ;
; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[3]              ; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[15] ; yes                    ;
; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[2]              ; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[15] ; yes                    ;
; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[1]              ; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[15] ; yes                    ;
; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[0]              ; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTH[15] ; yes                    ;
; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTL[15]             ; AD_FREQ_WORD:u_AD_FREQ_WORD|AD1_OUTL[15] ; yes                    ;
; Number of user-specified and inferred latches = 354  ;                                          ;                        ;
+------------------------------------------------------+------------------------------------------+------------------------+
Table restricted to first 100 entries. Note: All latches listed above may not be present at the end of synthesis due to various synthesis optimizations.


+------------------------------------------------------------+
; Registers Removed During Synthesis                         ;
+---------------------------------------+--------------------+
; Register name                         ; Reason for Removal ;
+---------------------------------------+--------------------+
; DA_PARAMETER_CTRL:inst7|CNT_A[6,7]    ; Lost fanout        ;
; DA_PARAMETER_CTRL:inst7|CNT_B[6,7]    ; Lost fanout        ;
; Total Number of Removed Registers = 4 ;                    ;
+---------------------------------------+--------------------+


+------------------------------------------------------+
; General Register Statistics                          ;
+----------------------------------------------+-------+
; Statistic                                    ; Value ;
+----------------------------------------------+-------+
; Total registers                              ; 1106  ;
; Number of registers using Synchronous Clear  ; 65    ;
; Number of registers using Synchronous Load   ; 80    ;
; Number of registers using Asynchronous Clear ; 368   ;
; Number of registers using Asynchronous Load  ; 0     ;
; Number of registers using Clock Enable       ; 500   ;
; Number of registers using Preset             ; 0     ;
+----------------------------------------------+-------+


+-----------------------------------------------------------------------------------------------------------------------+
; Inverted Register Statistics                                                                                          ;
+-------------------------------------------------------------------------------------------------------------+---------+
; Inverted Register                                                                                           ; Fan out ;
+-------------------------------------------------------------------------------------------------------------+---------+
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|a_graycounter_4p6:rdptr_g1p|counter5a0 ; 7       ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|a_graycounter_4p6:rdptr_g1p|counter5a0 ; 7       ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|a_graycounter_07c:wrptr_g1p|counter8a0 ; 7       ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|a_graycounter_07c:wrptr_g1p|counter8a0 ; 7       ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|a_graycounter_4p6:rdptr_g1p|parity6    ; 4       ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|a_graycounter_4p6:rdptr_g1p|parity6    ; 4       ;
; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|a_graycounter_07c:wrptr_g1p|parity9    ; 4       ;
; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|a_graycounter_07c:wrptr_g1p|parity9    ; 5       ;
; Total number of inverted registers = 8                                                                      ;         ;
+-------------------------------------------------------------------------------------------------------------+---------+


+------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Multiplexer Restructuring Statistics (Restructuring Performed)                                                                                             ;
+--------------------+-----------+---------------+----------------------+------------------------+------------+----------------------------------------------+
; Multiplexer Inputs ; Bus Width ; Baseline Area ; Area if Restructured ; Saving if Restructured ; Registered ; Example Multiplexer Output                   ;
+--------------------+-----------+---------------+----------------------+------------------------+------------+----------------------------------------------+
; 3:1                ; 12 bits   ; 24 LEs        ; 24 LEs               ; 0 LEs                  ; Yes        ; |TOP|DA_PARAMETER_CTRL:inst7|COUT_B_FINAL[4] ;
; 4:1                ; 8 bits    ; 16 LEs        ; 8 LEs                ; 8 LEs                  ; Yes        ; |TOP|DA_PARAMETER_CTRL:inst7|CNT_B[5]        ;
; 4:1                ; 8 bits    ; 16 LEs        ; 8 LEs                ; 8 LEs                  ; Yes        ; |TOP|DA_PARAMETER_CTRL:inst7|CNT_A[2]        ;
; 256:1              ; 14 bits   ; 2380 LEs      ; 28 LEs               ; 2352 LEs               ; Yes        ; |TOP|DA_WAVEFORM:inst5|wave_data_pipe1[5]    ;
; 256:1              ; 14 bits   ; 2380 LEs      ; 28 LEs               ; 2352 LEs               ; Yes        ; |TOP|DA_WAVEFORM:inst8|wave_data_pipe1[1]    ;
; 17:1               ; 4 bits    ; 44 LEs        ; 44 LEs               ; 0 LEs                  ; Yes        ; |TOP|FMC_CONTROL:inst|rd_data_reg[15]        ;
; 17:1               ; 11 bits   ; 121 LEs       ; 121 LEs              ; 0 LEs                  ; Yes        ; |TOP|FMC_CONTROL:inst|rd_data_reg[1]         ;
+--------------------+-----------+---------------+----------------------+------------------------+------------+----------------------------------------------+


+------------------------------------------------------------------+
; Source assignments for TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component ;
+---------------------------------+-------+------+-----------------+
; Assignment                      ; Value ; From ; To              ;
+---------------------------------+-------+------+-----------------+
; AUTO_SHIFT_REGISTER_RECOGNITION ; OFF   ; -    ; -               ;
+---------------------------------+-------+------+-----------------+


+---------------------------------------------------------------------------------------------+
; Source assignments for TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated ;
+---------------------------------------+-------+------+--------------------------------------+
; Assignment                            ; Value ; From ; To                                   ;
+---------------------------------------+-------+------+--------------------------------------+
; AUTO_SHIFT_REGISTER_RECOGNITION       ; OFF   ; -    ; -                                    ;
; REMOVE_DUPLICATE_REGISTERS            ; OFF   ; -    ; -                                    ;
; SYNCHRONIZER_IDENTIFICATION           ; OFF   ; -    ; -                                    ;
; SYNCHRONIZATION_REGISTER_CHAIN_LENGTH ; 2     ; -    ; -                                    ;
; POWER_UP_LEVEL                        ; LOW   ; -    ; wrptr_g                              ;
+---------------------------------------+-------+------+--------------------------------------+


+-------------------------------------------------------------------------------------------------------------------------+
; Source assignments for TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|a_graycounter_4p6:rdptr_g1p ;
+----------------+-------+------+-----------------------------------------------------------------------------------------+
; Assignment     ; Value ; From ; To                                                                                      ;
+----------------+-------+------+-----------------------------------------------------------------------------------------+
; POWER_UP_LEVEL ; HIGH  ; -    ; counter5a0                                                                              ;
; POWER_UP_LEVEL ; HIGH  ; -    ; parity6                                                                                 ;
+----------------+-------+------+-----------------------------------------------------------------------------------------+


+-------------------------------------------------------------------------------------------------------------------------+
; Source assignments for TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|a_graycounter_07c:wrptr_g1p ;
+----------------+-------+------+-----------------------------------------------------------------------------------------+
; Assignment     ; Value ; From ; To                                                                                      ;
+----------------+-------+------+-----------------------------------------------------------------------------------------+
; POWER_UP_LEVEL ; HIGH  ; -    ; counter8a0                                                                              ;
; POWER_UP_LEVEL ; HIGH  ; -    ; parity9                                                                                 ;
+----------------+-------+------+-----------------------------------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------+
; Source assignments for TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|altsyncram_ce41:fifo_ram ;
+---------------------------------+--------------------+------+--------------------------------------------------------+
; Assignment                      ; Value              ; From ; To                                                     ;
+---------------------------------+--------------------+------+--------------------------------------------------------+
; OPTIMIZE_POWER_DURING_SYNTHESIS ; NORMAL_COMPILATION ; -    ; -                                                      ;
+---------------------------------+--------------------+------+--------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------+
; Source assignments for TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp ;
+-----------------------------+------------------------+------+----------------------------------------------------------+
; Assignment                  ; Value                  ; From ; To                                                       ;
+-----------------------------+------------------------+------+----------------------------------------------------------+
; X_ON_VIOLATION_OPTION       ; OFF                    ; -    ; -                                                        ;
; SYNCHRONIZER_IDENTIFICATION ; FORCED_IF_ASYNCHRONOUS ; -    ; -                                                        ;
; PRESERVE_REGISTER           ; ON                     ; -    ; -                                                        ;
; DONT_MERGE_REGISTER         ; ON                     ; -    ; -                                                        ;
; ADV_NETLIST_OPT_ALLOWED     ; NEVER_ALLOW            ; -    ; -                                                        ;
+-----------------------------+------------------------+------+----------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------------------------------+
; Source assignments for TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12 ;
+---------------------------------+-------+------+---------------------------------------------------------------------------------------------+
; Assignment                      ; Value ; From ; To                                                                                          ;
+---------------------------------+-------+------+---------------------------------------------------------------------------------------------+
; AUTO_SHIFT_REGISTER_RECOGNITION ; OFF   ; -    ; -                                                                                           ;
+---------------------------------+-------+------+---------------------------------------------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------+
; Source assignments for TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp ;
+-----------------------------+------------------------+------+----------------------------------------------------------+
; Assignment                  ; Value                  ; From ; To                                                       ;
+-----------------------------+------------------------+------+----------------------------------------------------------+
; X_ON_VIOLATION_OPTION       ; OFF                    ; -    ; -                                                        ;
; SYNCHRONIZER_IDENTIFICATION ; FORCED_IF_ASYNCHRONOUS ; -    ; -                                                        ;
; PRESERVE_REGISTER           ; ON                     ; -    ; -                                                        ;
; DONT_MERGE_REGISTER         ; ON                     ; -    ; -                                                        ;
; ADV_NETLIST_OPT_ALLOWED     ; NEVER_ALLOW            ; -    ; -                                                        ;
+-----------------------------+------------------------+------+----------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------------------------------+
; Source assignments for TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15 ;
+---------------------------------+-------+------+---------------------------------------------------------------------------------------------+
; Assignment                      ; Value ; From ; To                                                                                          ;
+---------------------------------+-------+------+---------------------------------------------------------------------------------------------+
; AUTO_SHIFT_REGISTER_RECOGNITION ; OFF   ; -    ; -                                                                                           ;
+---------------------------------+-------+------+---------------------------------------------------------------------------------------------+


+------------------------------------------------------------------+
; Source assignments for TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component ;
+---------------------------------+-------+------+-----------------+
; Assignment                      ; Value ; From ; To              ;
+---------------------------------+-------+------+-----------------+
; AUTO_SHIFT_REGISTER_RECOGNITION ; OFF   ; -    ; -               ;
+---------------------------------+-------+------+-----------------+


+---------------------------------------------------------------------------------------------+
; Source assignments for TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated ;
+---------------------------------------+-------+------+--------------------------------------+
; Assignment                            ; Value ; From ; To                                   ;
+---------------------------------------+-------+------+--------------------------------------+
; AUTO_SHIFT_REGISTER_RECOGNITION       ; OFF   ; -    ; -                                    ;
; REMOVE_DUPLICATE_REGISTERS            ; OFF   ; -    ; -                                    ;
; SYNCHRONIZER_IDENTIFICATION           ; OFF   ; -    ; -                                    ;
; SYNCHRONIZATION_REGISTER_CHAIN_LENGTH ; 2     ; -    ; -                                    ;
; POWER_UP_LEVEL                        ; LOW   ; -    ; wrptr_g                              ;
+---------------------------------------+-------+------+--------------------------------------+


+-------------------------------------------------------------------------------------------------------------------------+
; Source assignments for TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|a_graycounter_4p6:rdptr_g1p ;
+----------------+-------+------+-----------------------------------------------------------------------------------------+
; Assignment     ; Value ; From ; To                                                                                      ;
+----------------+-------+------+-----------------------------------------------------------------------------------------+
; POWER_UP_LEVEL ; HIGH  ; -    ; counter5a0                                                                              ;
; POWER_UP_LEVEL ; HIGH  ; -    ; parity6                                                                                 ;
+----------------+-------+------+-----------------------------------------------------------------------------------------+


+-------------------------------------------------------------------------------------------------------------------------+
; Source assignments for TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|a_graycounter_07c:wrptr_g1p ;
+----------------+-------+------+-----------------------------------------------------------------------------------------+
; Assignment     ; Value ; From ; To                                                                                      ;
+----------------+-------+------+-----------------------------------------------------------------------------------------+
; POWER_UP_LEVEL ; HIGH  ; -    ; counter8a0                                                                              ;
; POWER_UP_LEVEL ; HIGH  ; -    ; parity9                                                                                 ;
+----------------+-------+------+-----------------------------------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------+
; Source assignments for TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|altsyncram_ce41:fifo_ram ;
+---------------------------------+--------------------+------+--------------------------------------------------------+
; Assignment                      ; Value              ; From ; To                                                     ;
+---------------------------------+--------------------+------+--------------------------------------------------------+
; OPTIMIZE_POWER_DURING_SYNTHESIS ; NORMAL_COMPILATION ; -    ; -                                                      ;
+---------------------------------+--------------------+------+--------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------+
; Source assignments for TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp ;
+-----------------------------+------------------------+------+----------------------------------------------------------+
; Assignment                  ; Value                  ; From ; To                                                       ;
+-----------------------------+------------------------+------+----------------------------------------------------------+
; X_ON_VIOLATION_OPTION       ; OFF                    ; -    ; -                                                        ;
; SYNCHRONIZER_IDENTIFICATION ; FORCED_IF_ASYNCHRONOUS ; -    ; -                                                        ;
; PRESERVE_REGISTER           ; ON                     ; -    ; -                                                        ;
; DONT_MERGE_REGISTER         ; ON                     ; -    ; -                                                        ;
; ADV_NETLIST_OPT_ALLOWED     ; NEVER_ALLOW            ; -    ; -                                                        ;
+-----------------------------+------------------------+------+----------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------------------------------+
; Source assignments for TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12 ;
+---------------------------------+-------+------+---------------------------------------------------------------------------------------------+
; Assignment                      ; Value ; From ; To                                                                                          ;
+---------------------------------+-------+------+---------------------------------------------------------------------------------------------+
; AUTO_SHIFT_REGISTER_RECOGNITION ; OFF   ; -    ; -                                                                                           ;
+---------------------------------+-------+------+---------------------------------------------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------+
; Source assignments for TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp ;
+-----------------------------+------------------------+------+----------------------------------------------------------+
; Assignment                  ; Value                  ; From ; To                                                       ;
+-----------------------------+------------------------+------+----------------------------------------------------------+
; X_ON_VIOLATION_OPTION       ; OFF                    ; -    ; -                                                        ;
; SYNCHRONIZER_IDENTIFICATION ; FORCED_IF_ASYNCHRONOUS ; -    ; -                                                        ;
; PRESERVE_REGISTER           ; ON                     ; -    ; -                                                        ;
; DONT_MERGE_REGISTER         ; ON                     ; -    ; -                                                        ;
; ADV_NETLIST_OPT_ALLOWED     ; NEVER_ALLOW            ; -    ; -                                                        ;
+-----------------------------+------------------------+------+----------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------------------------------+
; Source assignments for TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15 ;
+---------------------------------+-------+------+---------------------------------------------------------------------------------------------+
; Assignment                      ; Value ; From ; To                                                                                          ;
+---------------------------------+-------+------+---------------------------------------------------------------------------------------------+
; AUTO_SHIFT_REGISTER_RECOGNITION ; OFF   ; -    ; -                                                                                           ;
+---------------------------------+-------+------+---------------------------------------------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------------+
; Source assignments for DA_WAVEFORM:inst5|sin_rom:sin_rom_inst|altsyncram:altsyncram_component|altsyncram_hva1:auto_generated ;
+---------------------------------+--------------------+------+----------------------------------------------------------------+
; Assignment                      ; Value              ; From ; To                                                             ;
+---------------------------------+--------------------+------+----------------------------------------------------------------+
; OPTIMIZE_POWER_DURING_SYNTHESIS ; NORMAL_COMPILATION ; -    ; -                                                              ;
+---------------------------------+--------------------+------+----------------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------------------+
; Source assignments for DA_WAVEFORM:inst5|sqaure_rom:sqaure_rom_inst|altsyncram:altsyncram_component|altsyncram_j6b1:auto_generated ;
+---------------------------------+--------------------+------+----------------------------------------------------------------------+
; Assignment                      ; Value              ; From ; To                                                                   ;
+---------------------------------+--------------------+------+----------------------------------------------------------------------+
; OPTIMIZE_POWER_DURING_SYNTHESIS ; NORMAL_COMPILATION ; -    ; -                                                                    ;
+---------------------------------+--------------------+------+----------------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------------------------+
; Source assignments for DA_WAVEFORM:inst5|triangle_rom:triangle_rom_inst|altsyncram:altsyncram_component|altsyncram_ocb1:auto_generated ;
+---------------------------------+--------------------+------+--------------------------------------------------------------------------+
; Assignment                      ; Value              ; From ; To                                                                       ;
+---------------------------------+--------------------+------+--------------------------------------------------------------------------+
; OPTIMIZE_POWER_DURING_SYNTHESIS ; NORMAL_COMPILATION ; -    ; -                                                                        ;
+---------------------------------+--------------------+------+--------------------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------------------------+
; Source assignments for DA_WAVEFORM:inst5|sawtooth_rom:sawtooth_rom_inst|altsyncram:altsyncram_component|altsyncram_rdb1:auto_generated ;
+---------------------------------+--------------------+------+--------------------------------------------------------------------------+
; Assignment                      ; Value              ; From ; To                                                                       ;
+---------------------------------+--------------------+------+--------------------------------------------------------------------------+
; OPTIMIZE_POWER_DURING_SYNTHESIS ; NORMAL_COMPILATION ; -    ; -                                                                        ;
+---------------------------------+--------------------+------+--------------------------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------------+
; Source assignments for DA_WAVEFORM:inst8|sin_rom:sin_rom_inst|altsyncram:altsyncram_component|altsyncram_hva1:auto_generated ;
+---------------------------------+--------------------+------+----------------------------------------------------------------+
; Assignment                      ; Value              ; From ; To                                                             ;
+---------------------------------+--------------------+------+----------------------------------------------------------------+
; OPTIMIZE_POWER_DURING_SYNTHESIS ; NORMAL_COMPILATION ; -    ; -                                                              ;
+---------------------------------+--------------------+------+----------------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------------------+
; Source assignments for DA_WAVEFORM:inst8|sqaure_rom:sqaure_rom_inst|altsyncram:altsyncram_component|altsyncram_j6b1:auto_generated ;
+---------------------------------+--------------------+------+----------------------------------------------------------------------+
; Assignment                      ; Value              ; From ; To                                                                   ;
+---------------------------------+--------------------+------+----------------------------------------------------------------------+
; OPTIMIZE_POWER_DURING_SYNTHESIS ; NORMAL_COMPILATION ; -    ; -                                                                    ;
+---------------------------------+--------------------+------+----------------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------------------------+
; Source assignments for DA_WAVEFORM:inst8|triangle_rom:triangle_rom_inst|altsyncram:altsyncram_component|altsyncram_ocb1:auto_generated ;
+---------------------------------+--------------------+------+--------------------------------------------------------------------------+
; Assignment                      ; Value              ; From ; To                                                                       ;
+---------------------------------+--------------------+------+--------------------------------------------------------------------------+
; OPTIMIZE_POWER_DURING_SYNTHESIS ; NORMAL_COMPILATION ; -    ; -                                                                        ;
+---------------------------------+--------------------+------+--------------------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------------------------+
; Source assignments for DA_WAVEFORM:inst8|sawtooth_rom:sawtooth_rom_inst|altsyncram:altsyncram_component|altsyncram_rdb1:auto_generated ;
+---------------------------------+--------------------+------+--------------------------------------------------------------------------+
; Assignment                      ; Value              ; From ; To                                                                       ;
+---------------------------------+--------------------+------+--------------------------------------------------------------------------+
; OPTIMIZE_POWER_DURING_SYNTHESIS ; NORMAL_COMPILATION ; -    ; -                                                                        ;
+---------------------------------+--------------------+------+--------------------------------------------------------------------------+


+----------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: DA_PARAMETER_CTRL:inst7 ;
+----------------+------------------+----------------------------------+
; Parameter Name ; Value            ; Type                             ;
+----------------+------------------+----------------------------------+
; ADDR10         ; 0000000000001010 ; Unsigned Binary                  ;
; ADDR11         ; 0000000000001011 ; Unsigned Binary                  ;
; PHASE_WIDTH    ; 8                ; Signed Integer                   ;
+----------------+------------------+----------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+----------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: MYPLL:inst6|altpll:altpll_component ;
+-------------------------------+-------------------------+------------------------+
; Parameter Name                ; Value                   ; Type                   ;
+-------------------------------+-------------------------+------------------------+
; OPERATION_MODE                ; NORMAL                  ; Untyped                ;
; PLL_TYPE                      ; AUTO                    ; Untyped                ;
; LPM_HINT                      ; CBX_MODULE_PREFIX=MYPLL ; Untyped                ;
; QUALIFY_CONF_DONE             ; OFF                     ; Untyped                ;
; COMPENSATE_CLOCK              ; CLK0                    ; Untyped                ;
; SCAN_CHAIN                    ; LONG                    ; Untyped                ;
; PRIMARY_CLOCK                 ; INCLK0                  ; Untyped                ;
; INCLK0_INPUT_FREQUENCY        ; 20000                   ; Signed Integer         ;
; INCLK1_INPUT_FREQUENCY        ; 0                       ; Untyped                ;
; GATE_LOCK_SIGNAL              ; NO                      ; Untyped                ;
; GATE_LOCK_COUNTER             ; 0                       ; Untyped                ;
; LOCK_HIGH                     ; 1                       ; Untyped                ;
; LOCK_LOW                      ; 1                       ; Untyped                ;
; VALID_LOCK_MULTIPLIER         ; 1                       ; Untyped                ;
; INVALID_LOCK_MULTIPLIER       ; 5                       ; Untyped                ;
; SWITCH_OVER_ON_LOSSCLK        ; OFF                     ; Untyped                ;
; SWITCH_OVER_ON_GATED_LOCK     ; OFF                     ; Untyped                ;
; ENABLE_SWITCH_OVER_COUNTER    ; OFF                     ; Untyped                ;
; SKIP_VCO                      ; OFF                     ; Untyped                ;
; SWITCH_OVER_COUNTER           ; 0                       ; Untyped                ;
; SWITCH_OVER_TYPE              ; AUTO                    ; Untyped                ;
; FEEDBACK_SOURCE               ; EXTCLK0                 ; Untyped                ;
; BANDWIDTH                     ; 0                       ; Untyped                ;
; BANDWIDTH_TYPE                ; AUTO                    ; Untyped                ;
; SPREAD_FREQUENCY              ; 0                       ; Untyped                ;
; DOWN_SPREAD                   ; 0                       ; Untyped                ;
; SELF_RESET_ON_GATED_LOSS_LOCK ; OFF                     ; Untyped                ;
; SELF_RESET_ON_LOSS_LOCK       ; OFF                     ; Untyped                ;
; CLK9_MULTIPLY_BY              ; 0                       ; Untyped                ;
; CLK8_MULTIPLY_BY              ; 0                       ; Untyped                ;
; CLK7_MULTIPLY_BY              ; 0                       ; Untyped                ;
; CLK6_MULTIPLY_BY              ; 0                       ; Untyped                ;
; CLK5_MULTIPLY_BY              ; 1                       ; Untyped                ;
; CLK4_MULTIPLY_BY              ; 1                       ; Untyped                ;
; CLK3_MULTIPLY_BY              ; 1                       ; Untyped                ;
; CLK2_MULTIPLY_BY              ; 1                       ; Untyped                ;
; CLK1_MULTIPLY_BY              ; 1                       ; Untyped                ;
; CLK0_MULTIPLY_BY              ; 3                       ; Signed Integer         ;
; CLK9_DIVIDE_BY                ; 0                       ; Untyped                ;
; CLK8_DIVIDE_BY                ; 0                       ; Untyped                ;
; CLK7_DIVIDE_BY                ; 0                       ; Untyped                ;
; CLK6_DIVIDE_BY                ; 0                       ; Untyped                ;
; CLK5_DIVIDE_BY                ; 1                       ; Untyped                ;
; CLK4_DIVIDE_BY                ; 1                       ; Untyped                ;
; CLK3_DIVIDE_BY                ; 1                       ; Untyped                ;
; CLK2_DIVIDE_BY                ; 1                       ; Untyped                ;
; CLK1_DIVIDE_BY                ; 1                       ; Untyped                ;
; CLK0_DIVIDE_BY                ; 1                       ; Signed Integer         ;
; CLK9_PHASE_SHIFT              ; 0                       ; Untyped                ;
; CLK8_PHASE_SHIFT              ; 0                       ; Untyped                ;
; CLK7_PHASE_SHIFT              ; 0                       ; Untyped                ;
; CLK6_PHASE_SHIFT              ; 0                       ; Untyped                ;
; CLK5_PHASE_SHIFT              ; 0                       ; Untyped                ;
; CLK4_PHASE_SHIFT              ; 0                       ; Untyped                ;
; CLK3_PHASE_SHIFT              ; 0                       ; Untyped                ;
; CLK2_PHASE_SHIFT              ; 0                       ; Untyped                ;
; CLK1_PHASE_SHIFT              ; 0                       ; Untyped                ;
; CLK0_PHASE_SHIFT              ; 0                       ; Untyped                ;
; CLK5_TIME_DELAY               ; 0                       ; Untyped                ;
; CLK4_TIME_DELAY               ; 0                       ; Untyped                ;
; CLK3_TIME_DELAY               ; 0                       ; Untyped                ;
; CLK2_TIME_DELAY               ; 0                       ; Untyped                ;
; CLK1_TIME_DELAY               ; 0                       ; Untyped                ;
; CLK0_TIME_DELAY               ; 0                       ; Untyped                ;
; CLK9_DUTY_CYCLE               ; 50                      ; Untyped                ;
; CLK8_DUTY_CYCLE               ; 50                      ; Untyped                ;
; CLK7_DUTY_CYCLE               ; 50                      ; Untyped                ;
; CLK6_DUTY_CYCLE               ; 50                      ; Untyped                ;
; CLK5_DUTY_CYCLE               ; 50                      ; Untyped                ;
; CLK4_DUTY_CYCLE               ; 50                      ; Untyped                ;
; CLK3_DUTY_CYCLE               ; 50                      ; Untyped                ;
; CLK2_DUTY_CYCLE               ; 50                      ; Untyped                ;
; CLK1_DUTY_CYCLE               ; 50                      ; Untyped                ;
; CLK0_DUTY_CYCLE               ; 50                      ; Signed Integer         ;
; CLK9_USE_EVEN_COUNTER_MODE    ; OFF                     ; Untyped                ;
; CLK8_USE_EVEN_COUNTER_MODE    ; OFF                     ; Untyped                ;
; CLK7_USE_EVEN_COUNTER_MODE    ; OFF                     ; Untyped                ;
; CLK6_USE_EVEN_COUNTER_MODE    ; OFF                     ; Untyped                ;
; CLK5_USE_EVEN_COUNTER_MODE    ; OFF                     ; Untyped                ;
; CLK4_USE_EVEN_COUNTER_MODE    ; OFF                     ; Untyped                ;
; CLK3_USE_EVEN_COUNTER_MODE    ; OFF                     ; Untyped                ;
; CLK2_USE_EVEN_COUNTER_MODE    ; OFF                     ; Untyped                ;
; CLK1_USE_EVEN_COUNTER_MODE    ; OFF                     ; Untyped                ;
; CLK0_USE_EVEN_COUNTER_MODE    ; OFF                     ; Untyped                ;
; CLK9_USE_EVEN_COUNTER_VALUE   ; OFF                     ; Untyped                ;
; CLK8_USE_EVEN_COUNTER_VALUE   ; OFF                     ; Untyped                ;
; CLK7_USE_EVEN_COUNTER_VALUE   ; OFF                     ; Untyped                ;
; CLK6_USE_EVEN_COUNTER_VALUE   ; OFF                     ; Untyped                ;
; CLK5_USE_EVEN_COUNTER_VALUE   ; OFF                     ; Untyped                ;
; CLK4_USE_EVEN_COUNTER_VALUE   ; OFF                     ; Untyped                ;
; CLK3_USE_EVEN_COUNTER_VALUE   ; OFF                     ; Untyped                ;
; CLK2_USE_EVEN_COUNTER_VALUE   ; OFF                     ; Untyped                ;
; CLK1_USE_EVEN_COUNTER_VALUE   ; OFF                     ; Untyped                ;
; CLK0_USE_EVEN_COUNTER_VALUE   ; OFF                     ; Untyped                ;
; LOCK_WINDOW_UI                ;  0.05                   ; Untyped                ;
; LOCK_WINDOW_UI_BITS           ; UNUSED                  ; Untyped                ;
; VCO_RANGE_DETECTOR_LOW_BITS   ; UNUSED                  ; Untyped                ;
; VCO_RANGE_DETECTOR_HIGH_BITS  ; UNUSED                  ; Untyped                ;
; DPA_MULTIPLY_BY               ; 0                       ; Untyped                ;
; DPA_DIVIDE_BY                 ; 1                       ; Untyped                ;
; DPA_DIVIDER                   ; 0                       ; Untyped                ;
; EXTCLK3_MULTIPLY_BY           ; 1                       ; Untyped                ;
; EXTCLK2_MULTIPLY_BY           ; 1                       ; Untyped                ;
; EXTCLK1_MULTIPLY_BY           ; 1                       ; Untyped                ;
; EXTCLK0_MULTIPLY_BY           ; 1                       ; Untyped                ;
; EXTCLK3_DIVIDE_BY             ; 1                       ; Untyped                ;
; EXTCLK2_DIVIDE_BY             ; 1                       ; Untyped                ;
; EXTCLK1_DIVIDE_BY             ; 1                       ; Untyped                ;
; EXTCLK0_DIVIDE_BY             ; 1                       ; Untyped                ;
; EXTCLK3_PHASE_SHIFT           ; 0                       ; Untyped                ;
; EXTCLK2_PHASE_SHIFT           ; 0                       ; Untyped                ;
; EXTCLK1_PHASE_SHIFT           ; 0                       ; Untyped                ;
; EXTCLK0_PHASE_SHIFT           ; 0                       ; Untyped                ;
; EXTCLK3_TIME_DELAY            ; 0                       ; Untyped                ;
; EXTCLK2_TIME_DELAY            ; 0                       ; Untyped                ;
; EXTCLK1_TIME_DELAY            ; 0                       ; Untyped                ;
; EXTCLK0_TIME_DELAY            ; 0                       ; Untyped                ;
; EXTCLK3_DUTY_CYCLE            ; 50                      ; Untyped                ;
; EXTCLK2_DUTY_CYCLE            ; 50                      ; Untyped                ;
; EXTCLK1_DUTY_CYCLE            ; 50                      ; Untyped                ;
; EXTCLK0_DUTY_CYCLE            ; 50                      ; Untyped                ;
; VCO_MULTIPLY_BY               ; 0                       ; Untyped                ;
; VCO_DIVIDE_BY                 ; 0                       ; Untyped                ;
; SCLKOUT0_PHASE_SHIFT          ; 0                       ; Untyped                ;
; SCLKOUT1_PHASE_SHIFT          ; 0                       ; Untyped                ;
; VCO_MIN                       ; 0                       ; Untyped                ;
; VCO_MAX                       ; 0                       ; Untyped                ;
; VCO_CENTER                    ; 0                       ; Untyped                ;
; PFD_MIN                       ; 0                       ; Untyped                ;
; PFD_MAX                       ; 0                       ; Untyped                ;
; M_INITIAL                     ; 0                       ; Untyped                ;
; M                             ; 0                       ; Untyped                ;
; N                             ; 1                       ; Untyped                ;
; M2                            ; 1                       ; Untyped                ;
; N2                            ; 1                       ; Untyped                ;
; SS                            ; 1                       ; Untyped                ;
; C0_HIGH                       ; 0                       ; Untyped                ;
; C1_HIGH                       ; 0                       ; Untyped                ;
; C2_HIGH                       ; 0                       ; Untyped                ;
; C3_HIGH                       ; 0                       ; Untyped                ;
; C4_HIGH                       ; 0                       ; Untyped                ;
; C5_HIGH                       ; 0                       ; Untyped                ;
; C6_HIGH                       ; 0                       ; Untyped                ;
; C7_HIGH                       ; 0                       ; Untyped                ;
; C8_HIGH                       ; 0                       ; Untyped                ;
; C9_HIGH                       ; 0                       ; Untyped                ;
; C0_LOW                        ; 0                       ; Untyped                ;
; C1_LOW                        ; 0                       ; Untyped                ;
; C2_LOW                        ; 0                       ; Untyped                ;
; C3_LOW                        ; 0                       ; Untyped                ;
; C4_LOW                        ; 0                       ; Untyped                ;
; C5_LOW                        ; 0                       ; Untyped                ;
; C6_LOW                        ; 0                       ; Untyped                ;
; C7_LOW                        ; 0                       ; Untyped                ;
; C8_LOW                        ; 0                       ; Untyped                ;
; C9_LOW                        ; 0                       ; Untyped                ;
; C0_INITIAL                    ; 0                       ; Untyped                ;
; C1_INITIAL                    ; 0                       ; Untyped                ;
; C2_INITIAL                    ; 0                       ; Untyped                ;
; C3_INITIAL                    ; 0                       ; Untyped                ;
; C4_INITIAL                    ; 0                       ; Untyped                ;
; C5_INITIAL                    ; 0                       ; Untyped                ;
; C6_INITIAL                    ; 0                       ; Untyped                ;
; C7_INITIAL                    ; 0                       ; Untyped                ;
; C8_INITIAL                    ; 0                       ; Untyped                ;
; C9_INITIAL                    ; 0                       ; Untyped                ;
; C0_MODE                       ; BYPASS                  ; Untyped                ;
; C1_MODE                       ; BYPASS                  ; Untyped                ;
; C2_MODE                       ; BYPASS                  ; Untyped                ;
; C3_MODE                       ; BYPASS                  ; Untyped                ;
; C4_MODE                       ; BYPASS                  ; Untyped                ;
; C5_MODE                       ; BYPASS                  ; Untyped                ;
; C6_MODE                       ; BYPASS                  ; Untyped                ;
; C7_MODE                       ; BYPASS                  ; Untyped                ;
; C8_MODE                       ; BYPASS                  ; Untyped                ;
; C9_MODE                       ; BYPASS                  ; Untyped                ;
; C0_PH                         ; 0                       ; Untyped                ;
; C1_PH                         ; 0                       ; Untyped                ;
; C2_PH                         ; 0                       ; Untyped                ;
; C3_PH                         ; 0                       ; Untyped                ;
; C4_PH                         ; 0                       ; Untyped                ;
; C5_PH                         ; 0                       ; Untyped                ;
; C6_PH                         ; 0                       ; Untyped                ;
; C7_PH                         ; 0                       ; Untyped                ;
; C8_PH                         ; 0                       ; Untyped                ;
; C9_PH                         ; 0                       ; Untyped                ;
; L0_HIGH                       ; 1                       ; Untyped                ;
; L1_HIGH                       ; 1                       ; Untyped                ;
; G0_HIGH                       ; 1                       ; Untyped                ;
; G1_HIGH                       ; 1                       ; Untyped                ;
; G2_HIGH                       ; 1                       ; Untyped                ;
; G3_HIGH                       ; 1                       ; Untyped                ;
; E0_HIGH                       ; 1                       ; Untyped                ;
; E1_HIGH                       ; 1                       ; Untyped                ;
; E2_HIGH                       ; 1                       ; Untyped                ;
; E3_HIGH                       ; 1                       ; Untyped                ;
; L0_LOW                        ; 1                       ; Untyped                ;
; L1_LOW                        ; 1                       ; Untyped                ;
; G0_LOW                        ; 1                       ; Untyped                ;
; G1_LOW                        ; 1                       ; Untyped                ;
; G2_LOW                        ; 1                       ; Untyped                ;
; G3_LOW                        ; 1                       ; Untyped                ;
; E0_LOW                        ; 1                       ; Untyped                ;
; E1_LOW                        ; 1                       ; Untyped                ;
; E2_LOW                        ; 1                       ; Untyped                ;
; E3_LOW                        ; 1                       ; Untyped                ;
; L0_INITIAL                    ; 1                       ; Untyped                ;
; L1_INITIAL                    ; 1                       ; Untyped                ;
; G0_INITIAL                    ; 1                       ; Untyped                ;
; G1_INITIAL                    ; 1                       ; Untyped                ;
; G2_INITIAL                    ; 1                       ; Untyped                ;
; G3_INITIAL                    ; 1                       ; Untyped                ;
; E0_INITIAL                    ; 1                       ; Untyped                ;
; E1_INITIAL                    ; 1                       ; Untyped                ;
; E2_INITIAL                    ; 1                       ; Untyped                ;
; E3_INITIAL                    ; 1                       ; Untyped                ;
; L0_MODE                       ; BYPASS                  ; Untyped                ;
; L1_MODE                       ; BYPASS                  ; Untyped                ;
; G0_MODE                       ; BYPASS                  ; Untyped                ;
; G1_MODE                       ; BYPASS                  ; Untyped                ;
; G2_MODE                       ; BYPASS                  ; Untyped                ;
; G3_MODE                       ; BYPASS                  ; Untyped                ;
; E0_MODE                       ; BYPASS                  ; Untyped                ;
; E1_MODE                       ; BYPASS                  ; Untyped                ;
; E2_MODE                       ; BYPASS                  ; Untyped                ;
; E3_MODE                       ; BYPASS                  ; Untyped                ;
; L0_PH                         ; 0                       ; Untyped                ;
; L1_PH                         ; 0                       ; Untyped                ;
; G0_PH                         ; 0                       ; Untyped                ;
; G1_PH                         ; 0                       ; Untyped                ;
; G2_PH                         ; 0                       ; Untyped                ;
; G3_PH                         ; 0                       ; Untyped                ;
; E0_PH                         ; 0                       ; Untyped                ;
; E1_PH                         ; 0                       ; Untyped                ;
; E2_PH                         ; 0                       ; Untyped                ;
; E3_PH                         ; 0                       ; Untyped                ;
; M_PH                          ; 0                       ; Untyped                ;
; C1_USE_CASC_IN                ; OFF                     ; Untyped                ;
; C2_USE_CASC_IN                ; OFF                     ; Untyped                ;
; C3_USE_CASC_IN                ; OFF                     ; Untyped                ;
; C4_USE_CASC_IN                ; OFF                     ; Untyped                ;
; C5_USE_CASC_IN                ; OFF                     ; Untyped                ;
; C6_USE_CASC_IN                ; OFF                     ; Untyped                ;
; C7_USE_CASC_IN                ; OFF                     ; Untyped                ;
; C8_USE_CASC_IN                ; OFF                     ; Untyped                ;
; C9_USE_CASC_IN                ; OFF                     ; Untyped                ;
; CLK0_COUNTER                  ; G0                      ; Untyped                ;
; CLK1_COUNTER                  ; G0                      ; Untyped                ;
; CLK2_COUNTER                  ; G0                      ; Untyped                ;
; CLK3_COUNTER                  ; G0                      ; Untyped                ;
; CLK4_COUNTER                  ; G0                      ; Untyped                ;
; CLK5_COUNTER                  ; G0                      ; Untyped                ;
; CLK6_COUNTER                  ; E0                      ; Untyped                ;
; CLK7_COUNTER                  ; E1                      ; Untyped                ;
; CLK8_COUNTER                  ; E2                      ; Untyped                ;
; CLK9_COUNTER                  ; E3                      ; Untyped                ;
; L0_TIME_DELAY                 ; 0                       ; Untyped                ;
; L1_TIME_DELAY                 ; 0                       ; Untyped                ;
; G0_TIME_DELAY                 ; 0                       ; Untyped                ;
; G1_TIME_DELAY                 ; 0                       ; Untyped                ;
; G2_TIME_DELAY                 ; 0                       ; Untyped                ;
; G3_TIME_DELAY                 ; 0                       ; Untyped                ;
; E0_TIME_DELAY                 ; 0                       ; Untyped                ;
; E1_TIME_DELAY                 ; 0                       ; Untyped                ;
; E2_TIME_DELAY                 ; 0                       ; Untyped                ;
; E3_TIME_DELAY                 ; 0                       ; Untyped                ;
; M_TIME_DELAY                  ; 0                       ; Untyped                ;
; N_TIME_DELAY                  ; 0                       ; Untyped                ;
; EXTCLK3_COUNTER               ; E3                      ; Untyped                ;
; EXTCLK2_COUNTER               ; E2                      ; Untyped                ;
; EXTCLK1_COUNTER               ; E1                      ; Untyped                ;
; EXTCLK0_COUNTER               ; E0                      ; Untyped                ;
; ENABLE0_COUNTER               ; L0                      ; Untyped                ;
; ENABLE1_COUNTER               ; L0                      ; Untyped                ;
; CHARGE_PUMP_CURRENT           ; 2                       ; Untyped                ;
; LOOP_FILTER_R                 ;  1.000000               ; Untyped                ;
; LOOP_FILTER_C                 ; 5                       ; Untyped                ;
; CHARGE_PUMP_CURRENT_BITS      ; 9999                    ; Untyped                ;
; LOOP_FILTER_R_BITS            ; 9999                    ; Untyped                ;
; LOOP_FILTER_C_BITS            ; 9999                    ; Untyped                ;
; VCO_POST_SCALE                ; 0                       ; Untyped                ;
; CLK2_OUTPUT_FREQUENCY         ; 0                       ; Untyped                ;
; CLK1_OUTPUT_FREQUENCY         ; 0                       ; Untyped                ;
; CLK0_OUTPUT_FREQUENCY         ; 0                       ; Untyped                ;
; INTENDED_DEVICE_FAMILY        ; Cyclone IV E            ; Untyped                ;
; PORT_CLKENA0                  ; PORT_UNUSED             ; Untyped                ;
; PORT_CLKENA1                  ; PORT_UNUSED             ; Untyped                ;
; PORT_CLKENA2                  ; PORT_UNUSED             ; Untyped                ;
; PORT_CLKENA3                  ; PORT_UNUSED             ; Untyped                ;
; PORT_CLKENA4                  ; PORT_UNUSED             ; Untyped                ;
; PORT_CLKENA5                  ; PORT_UNUSED             ; Untyped                ;
; PORT_EXTCLKENA0               ; PORT_CONNECTIVITY       ; Untyped                ;
; PORT_EXTCLKENA1               ; PORT_CONNECTIVITY       ; Untyped                ;
; PORT_EXTCLKENA2               ; PORT_CONNECTIVITY       ; Untyped                ;
; PORT_EXTCLKENA3               ; PORT_CONNECTIVITY       ; Untyped                ;
; PORT_EXTCLK0                  ; PORT_UNUSED             ; Untyped                ;
; PORT_EXTCLK1                  ; PORT_UNUSED             ; Untyped                ;
; PORT_EXTCLK2                  ; PORT_UNUSED             ; Untyped                ;
; PORT_EXTCLK3                  ; PORT_UNUSED             ; Untyped                ;
; PORT_CLKBAD0                  ; PORT_UNUSED             ; Untyped                ;
; PORT_CLKBAD1                  ; PORT_UNUSED             ; Untyped                ;
; PORT_CLK0                     ; PORT_USED               ; Untyped                ;
; PORT_CLK1                     ; PORT_UNUSED             ; Untyped                ;
; PORT_CLK2                     ; PORT_UNUSED             ; Untyped                ;
; PORT_CLK3                     ; PORT_UNUSED             ; Untyped                ;
; PORT_CLK4                     ; PORT_UNUSED             ; Untyped                ;
; PORT_CLK5                     ; PORT_UNUSED             ; Untyped                ;
; PORT_CLK6                     ; PORT_UNUSED             ; Untyped                ;
; PORT_CLK7                     ; PORT_UNUSED             ; Untyped                ;
; PORT_CLK8                     ; PORT_UNUSED             ; Untyped                ;
; PORT_CLK9                     ; PORT_UNUSED             ; Untyped                ;
; PORT_SCANDATA                 ; PORT_UNUSED             ; Untyped                ;
; PORT_SCANDATAOUT              ; PORT_UNUSED             ; Untyped                ;
; PORT_SCANDONE                 ; PORT_UNUSED             ; Untyped                ;
; PORT_SCLKOUT1                 ; PORT_CONNECTIVITY       ; Untyped                ;
; PORT_SCLKOUT0                 ; PORT_CONNECTIVITY       ; Untyped                ;
; PORT_ACTIVECLOCK              ; PORT_UNUSED             ; Untyped                ;
; PORT_CLKLOSS                  ; PORT_UNUSED             ; Untyped                ;
; PORT_INCLK1                   ; PORT_UNUSED             ; Untyped                ;
; PORT_INCLK0                   ; PORT_USED               ; Untyped                ;
; PORT_FBIN                     ; PORT_UNUSED             ; Untyped                ;
; PORT_PLLENA                   ; PORT_UNUSED             ; Untyped                ;
; PORT_CLKSWITCH                ; PORT_UNUSED             ; Untyped                ;
; PORT_ARESET                   ; PORT_UNUSED             ; Untyped                ;
; PORT_PFDENA                   ; PORT_UNUSED             ; Untyped                ;
; PORT_SCANCLK                  ; PORT_UNUSED             ; Untyped                ;
; PORT_SCANACLR                 ; PORT_UNUSED             ; Untyped                ;
; PORT_SCANREAD                 ; PORT_UNUSED             ; Untyped                ;
; PORT_SCANWRITE                ; PORT_UNUSED             ; Untyped                ;
; PORT_ENABLE0                  ; PORT_CONNECTIVITY       ; Untyped                ;
; PORT_ENABLE1                  ; PORT_CONNECTIVITY       ; Untyped                ;
; PORT_LOCKED                   ; PORT_UNUSED             ; Untyped                ;
; PORT_CONFIGUPDATE             ; PORT_UNUSED             ; Untyped                ;
; PORT_FBOUT                    ; PORT_CONNECTIVITY       ; Untyped                ;
; PORT_PHASEDONE                ; PORT_UNUSED             ; Untyped                ;
; PORT_PHASESTEP                ; PORT_UNUSED             ; Untyped                ;
; PORT_PHASEUPDOWN              ; PORT_UNUSED             ; Untyped                ;
; PORT_SCANCLKENA               ; PORT_UNUSED             ; Untyped                ;
; PORT_PHASECOUNTERSELECT       ; PORT_UNUSED             ; Untyped                ;
; PORT_VCOOVERRANGE             ; PORT_CONNECTIVITY       ; Untyped                ;
; PORT_VCOUNDERRANGE            ; PORT_CONNECTIVITY       ; Untyped                ;
; M_TEST_SOURCE                 ; 5                       ; Untyped                ;
; C0_TEST_SOURCE                ; 5                       ; Untyped                ;
; C1_TEST_SOURCE                ; 5                       ; Untyped                ;
; C2_TEST_SOURCE                ; 5                       ; Untyped                ;
; C3_TEST_SOURCE                ; 5                       ; Untyped                ;
; C4_TEST_SOURCE                ; 5                       ; Untyped                ;
; C5_TEST_SOURCE                ; 5                       ; Untyped                ;
; C6_TEST_SOURCE                ; 5                       ; Untyped                ;
; C7_TEST_SOURCE                ; 5                       ; Untyped                ;
; C8_TEST_SOURCE                ; 5                       ; Untyped                ;
; C9_TEST_SOURCE                ; 5                       ; Untyped                ;
; CBXI_PARAMETER                ; MYPLL_altpll1           ; Untyped                ;
; VCO_FREQUENCY_CONTROL         ; AUTO                    ; Untyped                ;
; VCO_PHASE_SHIFT_STEP          ; 0                       ; Untyped                ;
; WIDTH_CLOCK                   ; 5                       ; Signed Integer         ;
; WIDTH_PHASECOUNTERSELECT      ; 4                       ; Untyped                ;
; USING_FBMIMICBIDIR_PORT       ; OFF                     ; Untyped                ;
; DEVICE_FAMILY                 ; Cyclone IV E            ; Untyped                ;
; SCAN_CHAIN_MIF_FILE           ; UNUSED                  ; Untyped                ;
; SIM_GATE_LOCK_DEVICE_BEHAVIOR ; OFF                     ; Untyped                ;
; AUTO_CARRY_CHAINS             ; ON                      ; AUTO_CARRY             ;
; IGNORE_CARRY_BUFFERS          ; OFF                     ; IGNORE_CARRY           ;
; AUTO_CASCADE_CHAINS           ; ON                      ; AUTO_CASCADE           ;
; IGNORE_CASCADE_BUFFERS        ; OFF                     ; IGNORE_CASCADE         ;
+-------------------------------+-------------------------+------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+----------------------------------------------------------------+
; Parameter Settings for User Entity Instance: MASTER_CTRL:inst3 ;
+----------------+------------------+----------------------------+
; Parameter Name ; Value            ; Type                       ;
+----------------+------------------+----------------------------+
; ADDR1          ; 0000000000000001 ; Unsigned Binary            ;
+----------------+------------------+----------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+--------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: AD_FREQ_MEASURE:u_AD_FREQ_MEASURE ;
+----------------+------------------+--------------------------------------------+
; Parameter Name ; Value            ; Type                                       ;
+----------------+------------------+--------------------------------------------+
; ADDR2          ; 0000000000000010 ; Unsigned Binary                            ;
; ADDR3          ; 0000000000000011 ; Unsigned Binary                            ;
; ADDR4          ; 0000000000000100 ; Unsigned Binary                            ;
; ADDR5          ; 0000000000000101 ; Unsigned Binary                            ;
; ADDR10         ; 0000000000001010 ; Unsigned Binary                            ;
; ADDR11         ; 0000000000001011 ; Unsigned Binary                            ;
; ADDR12         ; 0000000000001100 ; Unsigned Binary                            ;
; ADDR13         ; 0000000000001101 ; Unsigned Binary                            ;
+----------------+------------------+--------------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+--------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: AD_DATA_DEAL:u_AD_DATA_DEAL ;
+----------------+------------------+--------------------------------------+
; Parameter Name ; Value            ; Type                                 ;
+----------------+------------------+--------------------------------------+
; ADDR6          ; 0000000000000110 ; Unsigned Binary                      ;
; ADDR7          ; 0000000000000111 ; Unsigned Binary                      ;
; ADDR8          ; 0000000000001000 ; Unsigned Binary                      ;
; ADDR9          ; 0000000000001001 ; Unsigned Binary                      ;
+----------------+------------------+--------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+----------------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component ;
+-------------------------+--------------+-----------------------------------------------+
; Parameter Name          ; Value        ; Type                                          ;
+-------------------------+--------------+-----------------------------------------------+
; WIDTH_BYTEENA           ; 1            ; Untyped                                       ;
; AUTO_CARRY_CHAINS       ; ON           ; AUTO_CARRY                                    ;
; IGNORE_CARRY_BUFFERS    ; OFF          ; IGNORE_CARRY                                  ;
; AUTO_CASCADE_CHAINS     ; ON           ; AUTO_CASCADE                                  ;
; IGNORE_CASCADE_BUFFERS  ; OFF          ; IGNORE_CASCADE                                ;
; LPM_WIDTH               ; 12           ; Signed Integer                                ;
; LPM_NUMWORDS            ; 1024         ; Signed Integer                                ;
; LPM_WIDTHU              ; 10           ; Signed Integer                                ;
; LPM_SHOWAHEAD           ; OFF          ; Untyped                                       ;
; UNDERFLOW_CHECKING      ; ON           ; Untyped                                       ;
; OVERFLOW_CHECKING       ; ON           ; Untyped                                       ;
; USE_EAB                 ; ON           ; Untyped                                       ;
; ADD_RAM_OUTPUT_REGISTER ; OFF          ; Untyped                                       ;
; ENABLE_ECC              ; FALSE        ; Untyped                                       ;
; DELAY_RDUSEDW           ; 1            ; Untyped                                       ;
; DELAY_WRUSEDW           ; 1            ; Untyped                                       ;
; RDSYNC_DELAYPIPE        ; 4            ; Signed Integer                                ;
; WRSYNC_DELAYPIPE        ; 4            ; Signed Integer                                ;
; CLOCKS_ARE_SYNCHRONIZED ; FALSE        ; Untyped                                       ;
; MAXIMIZE_SPEED          ; 5            ; Untyped                                       ;
; DEVICE_FAMILY           ; Cyclone IV E ; Untyped                                       ;
; ADD_USEDW_MSB_BIT       ; OFF          ; Untyped                                       ;
; WRITE_ACLR_SYNCH        ; OFF          ; Untyped                                       ;
; READ_ACLR_SYNCH         ; OFF          ; Untyped                                       ;
; CBXI_PARAMETER          ; dcfifo_vve1  ; Untyped                                       ;
+-------------------------+--------------+-----------------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+--------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: AD_FREQ_WORD:u_AD_FREQ_WORD ;
+----------------+------------------+--------------------------------------+
; Parameter Name ; Value            ; Type                                 ;
+----------------+------------------+--------------------------------------+
; ADDR6          ; 0000000000000110 ; Unsigned Binary                      ;
; ADDR7          ; 0000000000000111 ; Unsigned Binary                      ;
; ADDR8          ; 0000000000001000 ; Unsigned Binary                      ;
; ADDR9          ; 0000000000001001 ; Unsigned Binary                      ;
+----------------+------------------+--------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+----------------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component ;
+-------------------------+--------------+-----------------------------------------------+
; Parameter Name          ; Value        ; Type                                          ;
+-------------------------+--------------+-----------------------------------------------+
; WIDTH_BYTEENA           ; 1            ; Untyped                                       ;
; AUTO_CARRY_CHAINS       ; ON           ; AUTO_CARRY                                    ;
; IGNORE_CARRY_BUFFERS    ; OFF          ; IGNORE_CARRY                                  ;
; AUTO_CASCADE_CHAINS     ; ON           ; AUTO_CASCADE                                  ;
; IGNORE_CASCADE_BUFFERS  ; OFF          ; IGNORE_CASCADE                                ;
; LPM_WIDTH               ; 12           ; Signed Integer                                ;
; LPM_NUMWORDS            ; 1024         ; Signed Integer                                ;
; LPM_WIDTHU              ; 10           ; Signed Integer                                ;
; LPM_SHOWAHEAD           ; OFF          ; Untyped                                       ;
; UNDERFLOW_CHECKING      ; ON           ; Untyped                                       ;
; OVERFLOW_CHECKING       ; ON           ; Untyped                                       ;
; USE_EAB                 ; ON           ; Untyped                                       ;
; ADD_RAM_OUTPUT_REGISTER ; OFF          ; Untyped                                       ;
; ENABLE_ECC              ; FALSE        ; Untyped                                       ;
; DELAY_RDUSEDW           ; 1            ; Untyped                                       ;
; DELAY_WRUSEDW           ; 1            ; Untyped                                       ;
; RDSYNC_DELAYPIPE        ; 4            ; Signed Integer                                ;
; WRSYNC_DELAYPIPE        ; 4            ; Signed Integer                                ;
; CLOCKS_ARE_SYNCHRONIZED ; FALSE        ; Untyped                                       ;
; MAXIMIZE_SPEED          ; 5            ; Untyped                                       ;
; DEVICE_FAMILY           ; Cyclone IV E ; Untyped                                       ;
; ADD_USEDW_MSB_BIT       ; OFF          ; Untyped                                       ;
; WRITE_ACLR_SYNCH        ; OFF          ; Untyped                                       ;
; READ_ACLR_SYNCH         ; OFF          ; Untyped                                       ;
; CBXI_PARAMETER          ; dcfifo_vve1  ; Untyped                                       ;
+-------------------------+--------------+-----------------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+----------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: DA_PARAMETER_DEAL:inst1 ;
+------------------+------------------+--------------------------------+
; Parameter Name   ; Value            ; Type                           ;
+------------------+------------------+--------------------------------+
; DA1_FREQ_H_ADDR2 ; 0000000000000010 ; Unsigned Binary                ;
; DA1_FREQ_L_ADDR3 ; 0000000000000011 ; Unsigned Binary                ;
; DA2_FREQ_H_ADDR4 ; 0000000000000100 ; Unsigned Binary                ;
; DA2_FREQ_L_ADDR5 ; 0000000000000101 ; Unsigned Binary                ;
; WAVE_SEL_ADDR12  ; 0000000000001100 ; Unsigned Binary                ;
; DA_STEP_ADDR13   ; 0000000000001101 ; Unsigned Binary                ;
; DA1_AMP_ADDR14   ; 0000000000001110 ; Unsigned Binary                ;
; DA2_AMP_ADDR15   ; 0000000000001111 ; Unsigned Binary                ;
+------------------+------------------+--------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+---------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: voltage_scaler_clocked_fast:inst10 ;
+-----------------+-------+-------------------------------------------------------+
; Parameter Name  ; Value ; Type                                                  ;
+-----------------+-------+-------------------------------------------------------+
; ROM_MAX         ; 16383 ; Signed Integer                                        ;
; DEFAULT_PEAK_MV ; 3080  ; Signed Integer                                        ;
; HALF_ROM_MAX    ; 8191  ; Signed Integer                                        ;
; SCALE_SHIFT     ; 12    ; Signed Integer                                        ;
; CORRECTION_NUM  ; 3080  ; Signed Integer                                        ;
; CORRECTION_DEN  ; 4096  ; Signed Integer                                        ;
+-----------------+-------+-------------------------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+----------------------------------------------------------------+
; Parameter Settings for User Entity Instance: DA_WAVEFORM:inst5 ;
+----------------+----------+------------------------------------+
; Parameter Name ; Value    ; Type                               ;
+----------------+----------+------------------------------------+
; SINE_WAVE      ; 00000000 ; Unsigned Binary                    ;
; SQUARE_WAVE    ; 00000001 ; Unsigned Binary                    ;
; TRIANGLE_WAVE  ; 00000010 ; Unsigned Binary                    ;
; SAWTOOTH_WAVE  ; 00000011 ; Unsigned Binary                    ;
+----------------+----------+------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+---------------------------------------------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: DA_WAVEFORM:inst5|sin_rom:sin_rom_inst|altsyncram:altsyncram_component ;
+------------------------------------+--------------------------+-----------------------------------------------------+
; Parameter Name                     ; Value                    ; Type                                                ;
+------------------------------------+--------------------------+-----------------------------------------------------+
; BYTE_SIZE_BLOCK                    ; 8                        ; Untyped                                             ;
; AUTO_CARRY_CHAINS                  ; ON                       ; AUTO_CARRY                                          ;
; IGNORE_CARRY_BUFFERS               ; OFF                      ; IGNORE_CARRY                                        ;
; AUTO_CASCADE_CHAINS                ; ON                       ; AUTO_CASCADE                                        ;
; IGNORE_CASCADE_BUFFERS             ; OFF                      ; IGNORE_CASCADE                                      ;
; WIDTH_BYTEENA                      ; 1                        ; Untyped                                             ;
; OPERATION_MODE                     ; ROM                      ; Untyped                                             ;
; WIDTH_A                            ; 14                       ; Signed Integer                                      ;
; WIDTHAD_A                          ; 6                        ; Signed Integer                                      ;
; NUMWORDS_A                         ; 64                       ; Signed Integer                                      ;
; OUTDATA_REG_A                      ; UNREGISTERED             ; Untyped                                             ;
; ADDRESS_ACLR_A                     ; NONE                     ; Untyped                                             ;
; OUTDATA_ACLR_A                     ; NONE                     ; Untyped                                             ;
; WRCONTROL_ACLR_A                   ; NONE                     ; Untyped                                             ;
; INDATA_ACLR_A                      ; NONE                     ; Untyped                                             ;
; BYTEENA_ACLR_A                     ; NONE                     ; Untyped                                             ;
; WIDTH_B                            ; 1                        ; Untyped                                             ;
; WIDTHAD_B                          ; 1                        ; Untyped                                             ;
; NUMWORDS_B                         ; 1                        ; Untyped                                             ;
; INDATA_REG_B                       ; CLOCK1                   ; Untyped                                             ;
; WRCONTROL_WRADDRESS_REG_B          ; CLOCK1                   ; Untyped                                             ;
; RDCONTROL_REG_B                    ; CLOCK1                   ; Untyped                                             ;
; ADDRESS_REG_B                      ; CLOCK1                   ; Untyped                                             ;
; OUTDATA_REG_B                      ; UNREGISTERED             ; Untyped                                             ;
; BYTEENA_REG_B                      ; CLOCK1                   ; Untyped                                             ;
; INDATA_ACLR_B                      ; NONE                     ; Untyped                                             ;
; WRCONTROL_ACLR_B                   ; NONE                     ; Untyped                                             ;
; ADDRESS_ACLR_B                     ; NONE                     ; Untyped                                             ;
; OUTDATA_ACLR_B                     ; NONE                     ; Untyped                                             ;
; RDCONTROL_ACLR_B                   ; NONE                     ; Untyped                                             ;
; BYTEENA_ACLR_B                     ; NONE                     ; Untyped                                             ;
; WIDTH_BYTEENA_A                    ; 1                        ; Signed Integer                                      ;
; WIDTH_BYTEENA_B                    ; 1                        ; Untyped                                             ;
; RAM_BLOCK_TYPE                     ; AUTO                     ; Untyped                                             ;
; BYTE_SIZE                          ; 8                        ; Untyped                                             ;
; READ_DURING_WRITE_MODE_MIXED_PORTS ; DONT_CARE                ; Untyped                                             ;
; READ_DURING_WRITE_MODE_PORT_A      ; NEW_DATA_NO_NBE_READ     ; Untyped                                             ;
; READ_DURING_WRITE_MODE_PORT_B      ; NEW_DATA_NO_NBE_READ     ; Untyped                                             ;
; INIT_FILE                          ; ../script/sine_64x14.mif ; Untyped                                             ;
; INIT_FILE_LAYOUT                   ; PORT_A                   ; Untyped                                             ;
; MAXIMUM_DEPTH                      ; 0                        ; Untyped                                             ;
; CLOCK_ENABLE_INPUT_A               ; BYPASS                   ; Untyped                                             ;
; CLOCK_ENABLE_INPUT_B               ; NORMAL                   ; Untyped                                             ;
; CLOCK_ENABLE_OUTPUT_A              ; BYPASS                   ; Untyped                                             ;
; CLOCK_ENABLE_OUTPUT_B              ; NORMAL                   ; Untyped                                             ;
; CLOCK_ENABLE_CORE_A                ; USE_INPUT_CLKEN          ; Untyped                                             ;
; CLOCK_ENABLE_CORE_B                ; USE_INPUT_CLKEN          ; Untyped                                             ;
; ENABLE_ECC                         ; FALSE                    ; Untyped                                             ;
; ECC_PIPELINE_STAGE_ENABLED         ; FALSE                    ; Untyped                                             ;
; WIDTH_ECCSTATUS                    ; 3                        ; Untyped                                             ;
; DEVICE_FAMILY                      ; Cyclone IV E             ; Untyped                                             ;
; CBXI_PARAMETER                     ; altsyncram_hva1          ; Untyped                                             ;
+------------------------------------+--------------------------+-----------------------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+---------------------------------------------------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: DA_WAVEFORM:inst5|sqaure_rom:sqaure_rom_inst|altsyncram:altsyncram_component ;
+------------------------------------+----------------------------+---------------------------------------------------------+
; Parameter Name                     ; Value                      ; Type                                                    ;
+------------------------------------+----------------------------+---------------------------------------------------------+
; BYTE_SIZE_BLOCK                    ; 8                          ; Untyped                                                 ;
; AUTO_CARRY_CHAINS                  ; ON                         ; AUTO_CARRY                                              ;
; IGNORE_CARRY_BUFFERS               ; OFF                        ; IGNORE_CARRY                                            ;
; AUTO_CASCADE_CHAINS                ; ON                         ; AUTO_CASCADE                                            ;
; IGNORE_CASCADE_BUFFERS             ; OFF                        ; IGNORE_CASCADE                                          ;
; WIDTH_BYTEENA                      ; 1                          ; Untyped                                                 ;
; OPERATION_MODE                     ; ROM                        ; Untyped                                                 ;
; WIDTH_A                            ; 14                         ; Signed Integer                                          ;
; WIDTHAD_A                          ; 6                          ; Signed Integer                                          ;
; NUMWORDS_A                         ; 64                         ; Signed Integer                                          ;
; OUTDATA_REG_A                      ; UNREGISTERED               ; Untyped                                                 ;
; ADDRESS_ACLR_A                     ; NONE                       ; Untyped                                                 ;
; OUTDATA_ACLR_A                     ; NONE                       ; Untyped                                                 ;
; WRCONTROL_ACLR_A                   ; NONE                       ; Untyped                                                 ;
; INDATA_ACLR_A                      ; NONE                       ; Untyped                                                 ;
; BYTEENA_ACLR_A                     ; NONE                       ; Untyped                                                 ;
; WIDTH_B                            ; 1                          ; Untyped                                                 ;
; WIDTHAD_B                          ; 1                          ; Untyped                                                 ;
; NUMWORDS_B                         ; 1                          ; Untyped                                                 ;
; INDATA_REG_B                       ; CLOCK1                     ; Untyped                                                 ;
; WRCONTROL_WRADDRESS_REG_B          ; CLOCK1                     ; Untyped                                                 ;
; RDCONTROL_REG_B                    ; CLOCK1                     ; Untyped                                                 ;
; ADDRESS_REG_B                      ; CLOCK1                     ; Untyped                                                 ;
; OUTDATA_REG_B                      ; UNREGISTERED               ; Untyped                                                 ;
; BYTEENA_REG_B                      ; CLOCK1                     ; Untyped                                                 ;
; INDATA_ACLR_B                      ; NONE                       ; Untyped                                                 ;
; WRCONTROL_ACLR_B                   ; NONE                       ; Untyped                                                 ;
; ADDRESS_ACLR_B                     ; NONE                       ; Untyped                                                 ;
; OUTDATA_ACLR_B                     ; NONE                       ; Untyped                                                 ;
; RDCONTROL_ACLR_B                   ; NONE                       ; Untyped                                                 ;
; BYTEENA_ACLR_B                     ; NONE                       ; Untyped                                                 ;
; WIDTH_BYTEENA_A                    ; 1                          ; Signed Integer                                          ;
; WIDTH_BYTEENA_B                    ; 1                          ; Untyped                                                 ;
; RAM_BLOCK_TYPE                     ; AUTO                       ; Untyped                                                 ;
; BYTE_SIZE                          ; 8                          ; Untyped                                                 ;
; READ_DURING_WRITE_MODE_MIXED_PORTS ; DONT_CARE                  ; Untyped                                                 ;
; READ_DURING_WRITE_MODE_PORT_A      ; NEW_DATA_NO_NBE_READ       ; Untyped                                                 ;
; READ_DURING_WRITE_MODE_PORT_B      ; NEW_DATA_NO_NBE_READ       ; Untyped                                                 ;
; INIT_FILE                          ; ../script/square_64x14.mif ; Untyped                                                 ;
; INIT_FILE_LAYOUT                   ; PORT_A                     ; Untyped                                                 ;
; MAXIMUM_DEPTH                      ; 0                          ; Untyped                                                 ;
; CLOCK_ENABLE_INPUT_A               ; BYPASS                     ; Untyped                                                 ;
; CLOCK_ENABLE_INPUT_B               ; NORMAL                     ; Untyped                                                 ;
; CLOCK_ENABLE_OUTPUT_A              ; BYPASS                     ; Untyped                                                 ;
; CLOCK_ENABLE_OUTPUT_B              ; NORMAL                     ; Untyped                                                 ;
; CLOCK_ENABLE_CORE_A                ; USE_INPUT_CLKEN            ; Untyped                                                 ;
; CLOCK_ENABLE_CORE_B                ; USE_INPUT_CLKEN            ; Untyped                                                 ;
; ENABLE_ECC                         ; FALSE                      ; Untyped                                                 ;
; ECC_PIPELINE_STAGE_ENABLED         ; FALSE                      ; Untyped                                                 ;
; WIDTH_ECCSTATUS                    ; 3                          ; Untyped                                                 ;
; DEVICE_FAMILY                      ; Cyclone IV E               ; Untyped                                                 ;
; CBXI_PARAMETER                     ; altsyncram_j6b1            ; Untyped                                                 ;
+------------------------------------+----------------------------+---------------------------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+-------------------------------------------------------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: DA_WAVEFORM:inst5|triangle_rom:triangle_rom_inst|altsyncram:altsyncram_component ;
+------------------------------------+------------------------------+-----------------------------------------------------------+
; Parameter Name                     ; Value                        ; Type                                                      ;
+------------------------------------+------------------------------+-----------------------------------------------------------+
; BYTE_SIZE_BLOCK                    ; 8                            ; Untyped                                                   ;
; AUTO_CARRY_CHAINS                  ; ON                           ; AUTO_CARRY                                                ;
; IGNORE_CARRY_BUFFERS               ; OFF                          ; IGNORE_CARRY                                              ;
; AUTO_CASCADE_CHAINS                ; ON                           ; AUTO_CASCADE                                              ;
; IGNORE_CASCADE_BUFFERS             ; OFF                          ; IGNORE_CASCADE                                            ;
; WIDTH_BYTEENA                      ; 1                            ; Untyped                                                   ;
; OPERATION_MODE                     ; ROM                          ; Untyped                                                   ;
; WIDTH_A                            ; 14                           ; Signed Integer                                            ;
; WIDTHAD_A                          ; 6                            ; Signed Integer                                            ;
; NUMWORDS_A                         ; 64                           ; Signed Integer                                            ;
; OUTDATA_REG_A                      ; UNREGISTERED                 ; Untyped                                                   ;
; ADDRESS_ACLR_A                     ; NONE                         ; Untyped                                                   ;
; OUTDATA_ACLR_A                     ; NONE                         ; Untyped                                                   ;
; WRCONTROL_ACLR_A                   ; NONE                         ; Untyped                                                   ;
; INDATA_ACLR_A                      ; NONE                         ; Untyped                                                   ;
; BYTEENA_ACLR_A                     ; NONE                         ; Untyped                                                   ;
; WIDTH_B                            ; 1                            ; Untyped                                                   ;
; WIDTHAD_B                          ; 1                            ; Untyped                                                   ;
; NUMWORDS_B                         ; 1                            ; Untyped                                                   ;
; INDATA_REG_B                       ; CLOCK1                       ; Untyped                                                   ;
; WRCONTROL_WRADDRESS_REG_B          ; CLOCK1                       ; Untyped                                                   ;
; RDCONTROL_REG_B                    ; CLOCK1                       ; Untyped                                                   ;
; ADDRESS_REG_B                      ; CLOCK1                       ; Untyped                                                   ;
; OUTDATA_REG_B                      ; UNREGISTERED                 ; Untyped                                                   ;
; BYTEENA_REG_B                      ; CLOCK1                       ; Untyped                                                   ;
; INDATA_ACLR_B                      ; NONE                         ; Untyped                                                   ;
; WRCONTROL_ACLR_B                   ; NONE                         ; Untyped                                                   ;
; ADDRESS_ACLR_B                     ; NONE                         ; Untyped                                                   ;
; OUTDATA_ACLR_B                     ; NONE                         ; Untyped                                                   ;
; RDCONTROL_ACLR_B                   ; NONE                         ; Untyped                                                   ;
; BYTEENA_ACLR_B                     ; NONE                         ; Untyped                                                   ;
; WIDTH_BYTEENA_A                    ; 1                            ; Signed Integer                                            ;
; WIDTH_BYTEENA_B                    ; 1                            ; Untyped                                                   ;
; RAM_BLOCK_TYPE                     ; AUTO                         ; Untyped                                                   ;
; BYTE_SIZE                          ; 8                            ; Untyped                                                   ;
; READ_DURING_WRITE_MODE_MIXED_PORTS ; DONT_CARE                    ; Untyped                                                   ;
; READ_DURING_WRITE_MODE_PORT_A      ; NEW_DATA_NO_NBE_READ         ; Untyped                                                   ;
; READ_DURING_WRITE_MODE_PORT_B      ; NEW_DATA_NO_NBE_READ         ; Untyped                                                   ;
; INIT_FILE                          ; ../script/triangle_64x14.mif ; Untyped                                                   ;
; INIT_FILE_LAYOUT                   ; PORT_A                       ; Untyped                                                   ;
; MAXIMUM_DEPTH                      ; 0                            ; Untyped                                                   ;
; CLOCK_ENABLE_INPUT_A               ; BYPASS                       ; Untyped                                                   ;
; CLOCK_ENABLE_INPUT_B               ; NORMAL                       ; Untyped                                                   ;
; CLOCK_ENABLE_OUTPUT_A              ; BYPASS                       ; Untyped                                                   ;
; CLOCK_ENABLE_OUTPUT_B              ; NORMAL                       ; Untyped                                                   ;
; CLOCK_ENABLE_CORE_A                ; USE_INPUT_CLKEN              ; Untyped                                                   ;
; CLOCK_ENABLE_CORE_B                ; USE_INPUT_CLKEN              ; Untyped                                                   ;
; ENABLE_ECC                         ; FALSE                        ; Untyped                                                   ;
; ECC_PIPELINE_STAGE_ENABLED         ; FALSE                        ; Untyped                                                   ;
; WIDTH_ECCSTATUS                    ; 3                            ; Untyped                                                   ;
; DEVICE_FAMILY                      ; Cyclone IV E                 ; Untyped                                                   ;
; CBXI_PARAMETER                     ; altsyncram_ocb1              ; Untyped                                                   ;
+------------------------------------+------------------------------+-----------------------------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+-------------------------------------------------------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: DA_WAVEFORM:inst5|sawtooth_rom:sawtooth_rom_inst|altsyncram:altsyncram_component ;
+------------------------------------+------------------------------+-----------------------------------------------------------+
; Parameter Name                     ; Value                        ; Type                                                      ;
+------------------------------------+------------------------------+-----------------------------------------------------------+
; BYTE_SIZE_BLOCK                    ; 8                            ; Untyped                                                   ;
; AUTO_CARRY_CHAINS                  ; ON                           ; AUTO_CARRY                                                ;
; IGNORE_CARRY_BUFFERS               ; OFF                          ; IGNORE_CARRY                                              ;
; AUTO_CASCADE_CHAINS                ; ON                           ; AUTO_CASCADE                                              ;
; IGNORE_CASCADE_BUFFERS             ; OFF                          ; IGNORE_CASCADE                                            ;
; WIDTH_BYTEENA                      ; 1                            ; Untyped                                                   ;
; OPERATION_MODE                     ; ROM                          ; Untyped                                                   ;
; WIDTH_A                            ; 14                           ; Signed Integer                                            ;
; WIDTHAD_A                          ; 6                            ; Signed Integer                                            ;
; NUMWORDS_A                         ; 64                           ; Signed Integer                                            ;
; OUTDATA_REG_A                      ; UNREGISTERED                 ; Untyped                                                   ;
; ADDRESS_ACLR_A                     ; NONE                         ; Untyped                                                   ;
; OUTDATA_ACLR_A                     ; NONE                         ; Untyped                                                   ;
; WRCONTROL_ACLR_A                   ; NONE                         ; Untyped                                                   ;
; INDATA_ACLR_A                      ; NONE                         ; Untyped                                                   ;
; BYTEENA_ACLR_A                     ; NONE                         ; Untyped                                                   ;
; WIDTH_B                            ; 1                            ; Untyped                                                   ;
; WIDTHAD_B                          ; 1                            ; Untyped                                                   ;
; NUMWORDS_B                         ; 1                            ; Untyped                                                   ;
; INDATA_REG_B                       ; CLOCK1                       ; Untyped                                                   ;
; WRCONTROL_WRADDRESS_REG_B          ; CLOCK1                       ; Untyped                                                   ;
; RDCONTROL_REG_B                    ; CLOCK1                       ; Untyped                                                   ;
; ADDRESS_REG_B                      ; CLOCK1                       ; Untyped                                                   ;
; OUTDATA_REG_B                      ; UNREGISTERED                 ; Untyped                                                   ;
; BYTEENA_REG_B                      ; CLOCK1                       ; Untyped                                                   ;
; INDATA_ACLR_B                      ; NONE                         ; Untyped                                                   ;
; WRCONTROL_ACLR_B                   ; NONE                         ; Untyped                                                   ;
; ADDRESS_ACLR_B                     ; NONE                         ; Untyped                                                   ;
; OUTDATA_ACLR_B                     ; NONE                         ; Untyped                                                   ;
; RDCONTROL_ACLR_B                   ; NONE                         ; Untyped                                                   ;
; BYTEENA_ACLR_B                     ; NONE                         ; Untyped                                                   ;
; WIDTH_BYTEENA_A                    ; 1                            ; Signed Integer                                            ;
; WIDTH_BYTEENA_B                    ; 1                            ; Untyped                                                   ;
; RAM_BLOCK_TYPE                     ; AUTO                         ; Untyped                                                   ;
; BYTE_SIZE                          ; 8                            ; Untyped                                                   ;
; READ_DURING_WRITE_MODE_MIXED_PORTS ; DONT_CARE                    ; Untyped                                                   ;
; READ_DURING_WRITE_MODE_PORT_A      ; NEW_DATA_NO_NBE_READ         ; Untyped                                                   ;
; READ_DURING_WRITE_MODE_PORT_B      ; NEW_DATA_NO_NBE_READ         ; Untyped                                                   ;
; INIT_FILE                          ; ../script/sawtooth_64x14.mif ; Untyped                                                   ;
; INIT_FILE_LAYOUT                   ; PORT_A                       ; Untyped                                                   ;
; MAXIMUM_DEPTH                      ; 0                            ; Untyped                                                   ;
; CLOCK_ENABLE_INPUT_A               ; BYPASS                       ; Untyped                                                   ;
; CLOCK_ENABLE_INPUT_B               ; NORMAL                       ; Untyped                                                   ;
; CLOCK_ENABLE_OUTPUT_A              ; BYPASS                       ; Untyped                                                   ;
; CLOCK_ENABLE_OUTPUT_B              ; NORMAL                       ; Untyped                                                   ;
; CLOCK_ENABLE_CORE_A                ; USE_INPUT_CLKEN              ; Untyped                                                   ;
; CLOCK_ENABLE_CORE_B                ; USE_INPUT_CLKEN              ; Untyped                                                   ;
; ENABLE_ECC                         ; FALSE                        ; Untyped                                                   ;
; ECC_PIPELINE_STAGE_ENABLED         ; FALSE                        ; Untyped                                                   ;
; WIDTH_ECCSTATUS                    ; 3                            ; Untyped                                                   ;
; DEVICE_FAMILY                      ; Cyclone IV E                 ; Untyped                                                   ;
; CBXI_PARAMETER                     ; altsyncram_rdb1              ; Untyped                                                   ;
+------------------------------------+------------------------------+-----------------------------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+---------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: voltage_scaler_clocked_fast:inst14 ;
+-----------------+-------+-------------------------------------------------------+
; Parameter Name  ; Value ; Type                                                  ;
+-----------------+-------+-------------------------------------------------------+
; ROM_MAX         ; 16383 ; Signed Integer                                        ;
; DEFAULT_PEAK_MV ; 3080  ; Signed Integer                                        ;
; HALF_ROM_MAX    ; 8191  ; Signed Integer                                        ;
; SCALE_SHIFT     ; 12    ; Signed Integer                                        ;
; CORRECTION_NUM  ; 3080  ; Signed Integer                                        ;
; CORRECTION_DEN  ; 4096  ; Signed Integer                                        ;
+-----------------+-------+-------------------------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+----------------------------------------------------------------+
; Parameter Settings for User Entity Instance: DA_WAVEFORM:inst8 ;
+----------------+----------+------------------------------------+
; Parameter Name ; Value    ; Type                               ;
+----------------+----------+------------------------------------+
; SINE_WAVE      ; 00000000 ; Unsigned Binary                    ;
; SQUARE_WAVE    ; 00000001 ; Unsigned Binary                    ;
; TRIANGLE_WAVE  ; 00000010 ; Unsigned Binary                    ;
; SAWTOOTH_WAVE  ; 00000011 ; Unsigned Binary                    ;
+----------------+----------+------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+---------------------------------------------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: DA_WAVEFORM:inst8|sin_rom:sin_rom_inst|altsyncram:altsyncram_component ;
+------------------------------------+--------------------------+-----------------------------------------------------+
; Parameter Name                     ; Value                    ; Type                                                ;
+------------------------------------+--------------------------+-----------------------------------------------------+
; BYTE_SIZE_BLOCK                    ; 8                        ; Untyped                                             ;
; AUTO_CARRY_CHAINS                  ; ON                       ; AUTO_CARRY                                          ;
; IGNORE_CARRY_BUFFERS               ; OFF                      ; IGNORE_CARRY                                        ;
; AUTO_CASCADE_CHAINS                ; ON                       ; AUTO_CASCADE                                        ;
; IGNORE_CASCADE_BUFFERS             ; OFF                      ; IGNORE_CASCADE                                      ;
; WIDTH_BYTEENA                      ; 1                        ; Untyped                                             ;
; OPERATION_MODE                     ; ROM                      ; Untyped                                             ;
; WIDTH_A                            ; 14                       ; Signed Integer                                      ;
; WIDTHAD_A                          ; 6                        ; Signed Integer                                      ;
; NUMWORDS_A                         ; 64                       ; Signed Integer                                      ;
; OUTDATA_REG_A                      ; UNREGISTERED             ; Untyped                                             ;
; ADDRESS_ACLR_A                     ; NONE                     ; Untyped                                             ;
; OUTDATA_ACLR_A                     ; NONE                     ; Untyped                                             ;
; WRCONTROL_ACLR_A                   ; NONE                     ; Untyped                                             ;
; INDATA_ACLR_A                      ; NONE                     ; Untyped                                             ;
; BYTEENA_ACLR_A                     ; NONE                     ; Untyped                                             ;
; WIDTH_B                            ; 1                        ; Untyped                                             ;
; WIDTHAD_B                          ; 1                        ; Untyped                                             ;
; NUMWORDS_B                         ; 1                        ; Untyped                                             ;
; INDATA_REG_B                       ; CLOCK1                   ; Untyped                                             ;
; WRCONTROL_WRADDRESS_REG_B          ; CLOCK1                   ; Untyped                                             ;
; RDCONTROL_REG_B                    ; CLOCK1                   ; Untyped                                             ;
; ADDRESS_REG_B                      ; CLOCK1                   ; Untyped                                             ;
; OUTDATA_REG_B                      ; UNREGISTERED             ; Untyped                                             ;
; BYTEENA_REG_B                      ; CLOCK1                   ; Untyped                                             ;
; INDATA_ACLR_B                      ; NONE                     ; Untyped                                             ;
; WRCONTROL_ACLR_B                   ; NONE                     ; Untyped                                             ;
; ADDRESS_ACLR_B                     ; NONE                     ; Untyped                                             ;
; OUTDATA_ACLR_B                     ; NONE                     ; Untyped                                             ;
; RDCONTROL_ACLR_B                   ; NONE                     ; Untyped                                             ;
; BYTEENA_ACLR_B                     ; NONE                     ; Untyped                                             ;
; WIDTH_BYTEENA_A                    ; 1                        ; Signed Integer                                      ;
; WIDTH_BYTEENA_B                    ; 1                        ; Untyped                                             ;
; RAM_BLOCK_TYPE                     ; AUTO                     ; Untyped                                             ;
; BYTE_SIZE                          ; 8                        ; Untyped                                             ;
; READ_DURING_WRITE_MODE_MIXED_PORTS ; DONT_CARE                ; Untyped                                             ;
; READ_DURING_WRITE_MODE_PORT_A      ; NEW_DATA_NO_NBE_READ     ; Untyped                                             ;
; READ_DURING_WRITE_MODE_PORT_B      ; NEW_DATA_NO_NBE_READ     ; Untyped                                             ;
; INIT_FILE                          ; ../script/sine_64x14.mif ; Untyped                                             ;
; INIT_FILE_LAYOUT                   ; PORT_A                   ; Untyped                                             ;
; MAXIMUM_DEPTH                      ; 0                        ; Untyped                                             ;
; CLOCK_ENABLE_INPUT_A               ; BYPASS                   ; Untyped                                             ;
; CLOCK_ENABLE_INPUT_B               ; NORMAL                   ; Untyped                                             ;
; CLOCK_ENABLE_OUTPUT_A              ; BYPASS                   ; Untyped                                             ;
; CLOCK_ENABLE_OUTPUT_B              ; NORMAL                   ; Untyped                                             ;
; CLOCK_ENABLE_CORE_A                ; USE_INPUT_CLKEN          ; Untyped                                             ;
; CLOCK_ENABLE_CORE_B                ; USE_INPUT_CLKEN          ; Untyped                                             ;
; ENABLE_ECC                         ; FALSE                    ; Untyped                                             ;
; ECC_PIPELINE_STAGE_ENABLED         ; FALSE                    ; Untyped                                             ;
; WIDTH_ECCSTATUS                    ; 3                        ; Untyped                                             ;
; DEVICE_FAMILY                      ; Cyclone IV E             ; Untyped                                             ;
; CBXI_PARAMETER                     ; altsyncram_hva1          ; Untyped                                             ;
+------------------------------------+--------------------------+-----------------------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+---------------------------------------------------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: DA_WAVEFORM:inst8|sqaure_rom:sqaure_rom_inst|altsyncram:altsyncram_component ;
+------------------------------------+----------------------------+---------------------------------------------------------+
; Parameter Name                     ; Value                      ; Type                                                    ;
+------------------------------------+----------------------------+---------------------------------------------------------+
; BYTE_SIZE_BLOCK                    ; 8                          ; Untyped                                                 ;
; AUTO_CARRY_CHAINS                  ; ON                         ; AUTO_CARRY                                              ;
; IGNORE_CARRY_BUFFERS               ; OFF                        ; IGNORE_CARRY                                            ;
; AUTO_CASCADE_CHAINS                ; ON                         ; AUTO_CASCADE                                            ;
; IGNORE_CASCADE_BUFFERS             ; OFF                        ; IGNORE_CASCADE                                          ;
; WIDTH_BYTEENA                      ; 1                          ; Untyped                                                 ;
; OPERATION_MODE                     ; ROM                        ; Untyped                                                 ;
; WIDTH_A                            ; 14                         ; Signed Integer                                          ;
; WIDTHAD_A                          ; 6                          ; Signed Integer                                          ;
; NUMWORDS_A                         ; 64                         ; Signed Integer                                          ;
; OUTDATA_REG_A                      ; UNREGISTERED               ; Untyped                                                 ;
; ADDRESS_ACLR_A                     ; NONE                       ; Untyped                                                 ;
; OUTDATA_ACLR_A                     ; NONE                       ; Untyped                                                 ;
; WRCONTROL_ACLR_A                   ; NONE                       ; Untyped                                                 ;
; INDATA_ACLR_A                      ; NONE                       ; Untyped                                                 ;
; BYTEENA_ACLR_A                     ; NONE                       ; Untyped                                                 ;
; WIDTH_B                            ; 1                          ; Untyped                                                 ;
; WIDTHAD_B                          ; 1                          ; Untyped                                                 ;
; NUMWORDS_B                         ; 1                          ; Untyped                                                 ;
; INDATA_REG_B                       ; CLOCK1                     ; Untyped                                                 ;
; WRCONTROL_WRADDRESS_REG_B          ; CLOCK1                     ; Untyped                                                 ;
; RDCONTROL_REG_B                    ; CLOCK1                     ; Untyped                                                 ;
; ADDRESS_REG_B                      ; CLOCK1                     ; Untyped                                                 ;
; OUTDATA_REG_B                      ; UNREGISTERED               ; Untyped                                                 ;
; BYTEENA_REG_B                      ; CLOCK1                     ; Untyped                                                 ;
; INDATA_ACLR_B                      ; NONE                       ; Untyped                                                 ;
; WRCONTROL_ACLR_B                   ; NONE                       ; Untyped                                                 ;
; ADDRESS_ACLR_B                     ; NONE                       ; Untyped                                                 ;
; OUTDATA_ACLR_B                     ; NONE                       ; Untyped                                                 ;
; RDCONTROL_ACLR_B                   ; NONE                       ; Untyped                                                 ;
; BYTEENA_ACLR_B                     ; NONE                       ; Untyped                                                 ;
; WIDTH_BYTEENA_A                    ; 1                          ; Signed Integer                                          ;
; WIDTH_BYTEENA_B                    ; 1                          ; Untyped                                                 ;
; RAM_BLOCK_TYPE                     ; AUTO                       ; Untyped                                                 ;
; BYTE_SIZE                          ; 8                          ; Untyped                                                 ;
; READ_DURING_WRITE_MODE_MIXED_PORTS ; DONT_CARE                  ; Untyped                                                 ;
; READ_DURING_WRITE_MODE_PORT_A      ; NEW_DATA_NO_NBE_READ       ; Untyped                                                 ;
; READ_DURING_WRITE_MODE_PORT_B      ; NEW_DATA_NO_NBE_READ       ; Untyped                                                 ;
; INIT_FILE                          ; ../script/square_64x14.mif ; Untyped                                                 ;
; INIT_FILE_LAYOUT                   ; PORT_A                     ; Untyped                                                 ;
; MAXIMUM_DEPTH                      ; 0                          ; Untyped                                                 ;
; CLOCK_ENABLE_INPUT_A               ; BYPASS                     ; Untyped                                                 ;
; CLOCK_ENABLE_INPUT_B               ; NORMAL                     ; Untyped                                                 ;
; CLOCK_ENABLE_OUTPUT_A              ; BYPASS                     ; Untyped                                                 ;
; CLOCK_ENABLE_OUTPUT_B              ; NORMAL                     ; Untyped                                                 ;
; CLOCK_ENABLE_CORE_A                ; USE_INPUT_CLKEN            ; Untyped                                                 ;
; CLOCK_ENABLE_CORE_B                ; USE_INPUT_CLKEN            ; Untyped                                                 ;
; ENABLE_ECC                         ; FALSE                      ; Untyped                                                 ;
; ECC_PIPELINE_STAGE_ENABLED         ; FALSE                      ; Untyped                                                 ;
; WIDTH_ECCSTATUS                    ; 3                          ; Untyped                                                 ;
; DEVICE_FAMILY                      ; Cyclone IV E               ; Untyped                                                 ;
; CBXI_PARAMETER                     ; altsyncram_j6b1            ; Untyped                                                 ;
+------------------------------------+----------------------------+---------------------------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+-------------------------------------------------------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: DA_WAVEFORM:inst8|triangle_rom:triangle_rom_inst|altsyncram:altsyncram_component ;
+------------------------------------+------------------------------+-----------------------------------------------------------+
; Parameter Name                     ; Value                        ; Type                                                      ;
+------------------------------------+------------------------------+-----------------------------------------------------------+
; BYTE_SIZE_BLOCK                    ; 8                            ; Untyped                                                   ;
; AUTO_CARRY_CHAINS                  ; ON                           ; AUTO_CARRY                                                ;
; IGNORE_CARRY_BUFFERS               ; OFF                          ; IGNORE_CARRY                                              ;
; AUTO_CASCADE_CHAINS                ; ON                           ; AUTO_CASCADE                                              ;
; IGNORE_CASCADE_BUFFERS             ; OFF                          ; IGNORE_CASCADE                                            ;
; WIDTH_BYTEENA                      ; 1                            ; Untyped                                                   ;
; OPERATION_MODE                     ; ROM                          ; Untyped                                                   ;
; WIDTH_A                            ; 14                           ; Signed Integer                                            ;
; WIDTHAD_A                          ; 6                            ; Signed Integer                                            ;
; NUMWORDS_A                         ; 64                           ; Signed Integer                                            ;
; OUTDATA_REG_A                      ; UNREGISTERED                 ; Untyped                                                   ;
; ADDRESS_ACLR_A                     ; NONE                         ; Untyped                                                   ;
; OUTDATA_ACLR_A                     ; NONE                         ; Untyped                                                   ;
; WRCONTROL_ACLR_A                   ; NONE                         ; Untyped                                                   ;
; INDATA_ACLR_A                      ; NONE                         ; Untyped                                                   ;
; BYTEENA_ACLR_A                     ; NONE                         ; Untyped                                                   ;
; WIDTH_B                            ; 1                            ; Untyped                                                   ;
; WIDTHAD_B                          ; 1                            ; Untyped                                                   ;
; NUMWORDS_B                         ; 1                            ; Untyped                                                   ;
; INDATA_REG_B                       ; CLOCK1                       ; Untyped                                                   ;
; WRCONTROL_WRADDRESS_REG_B          ; CLOCK1                       ; Untyped                                                   ;
; RDCONTROL_REG_B                    ; CLOCK1                       ; Untyped                                                   ;
; ADDRESS_REG_B                      ; CLOCK1                       ; Untyped                                                   ;
; OUTDATA_REG_B                      ; UNREGISTERED                 ; Untyped                                                   ;
; BYTEENA_REG_B                      ; CLOCK1                       ; Untyped                                                   ;
; INDATA_ACLR_B                      ; NONE                         ; Untyped                                                   ;
; WRCONTROL_ACLR_B                   ; NONE                         ; Untyped                                                   ;
; ADDRESS_ACLR_B                     ; NONE                         ; Untyped                                                   ;
; OUTDATA_ACLR_B                     ; NONE                         ; Untyped                                                   ;
; RDCONTROL_ACLR_B                   ; NONE                         ; Untyped                                                   ;
; BYTEENA_ACLR_B                     ; NONE                         ; Untyped                                                   ;
; WIDTH_BYTEENA_A                    ; 1                            ; Signed Integer                                            ;
; WIDTH_BYTEENA_B                    ; 1                            ; Untyped                                                   ;
; RAM_BLOCK_TYPE                     ; AUTO                         ; Untyped                                                   ;
; BYTE_SIZE                          ; 8                            ; Untyped                                                   ;
; READ_DURING_WRITE_MODE_MIXED_PORTS ; DONT_CARE                    ; Untyped                                                   ;
; READ_DURING_WRITE_MODE_PORT_A      ; NEW_DATA_NO_NBE_READ         ; Untyped                                                   ;
; READ_DURING_WRITE_MODE_PORT_B      ; NEW_DATA_NO_NBE_READ         ; Untyped                                                   ;
; INIT_FILE                          ; ../script/triangle_64x14.mif ; Untyped                                                   ;
; INIT_FILE_LAYOUT                   ; PORT_A                       ; Untyped                                                   ;
; MAXIMUM_DEPTH                      ; 0                            ; Untyped                                                   ;
; CLOCK_ENABLE_INPUT_A               ; BYPASS                       ; Untyped                                                   ;
; CLOCK_ENABLE_INPUT_B               ; NORMAL                       ; Untyped                                                   ;
; CLOCK_ENABLE_OUTPUT_A              ; BYPASS                       ; Untyped                                                   ;
; CLOCK_ENABLE_OUTPUT_B              ; NORMAL                       ; Untyped                                                   ;
; CLOCK_ENABLE_CORE_A                ; USE_INPUT_CLKEN              ; Untyped                                                   ;
; CLOCK_ENABLE_CORE_B                ; USE_INPUT_CLKEN              ; Untyped                                                   ;
; ENABLE_ECC                         ; FALSE                        ; Untyped                                                   ;
; ECC_PIPELINE_STAGE_ENABLED         ; FALSE                        ; Untyped                                                   ;
; WIDTH_ECCSTATUS                    ; 3                            ; Untyped                                                   ;
; DEVICE_FAMILY                      ; Cyclone IV E                 ; Untyped                                                   ;
; CBXI_PARAMETER                     ; altsyncram_ocb1              ; Untyped                                                   ;
+------------------------------------+------------------------------+-----------------------------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+-------------------------------------------------------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: DA_WAVEFORM:inst8|sawtooth_rom:sawtooth_rom_inst|altsyncram:altsyncram_component ;
+------------------------------------+------------------------------+-----------------------------------------------------------+
; Parameter Name                     ; Value                        ; Type                                                      ;
+------------------------------------+------------------------------+-----------------------------------------------------------+
; BYTE_SIZE_BLOCK                    ; 8                            ; Untyped                                                   ;
; AUTO_CARRY_CHAINS                  ; ON                           ; AUTO_CARRY                                                ;
; IGNORE_CARRY_BUFFERS               ; OFF                          ; IGNORE_CARRY                                              ;
; AUTO_CASCADE_CHAINS                ; ON                           ; AUTO_CASCADE                                              ;
; IGNORE_CASCADE_BUFFERS             ; OFF                          ; IGNORE_CASCADE                                            ;
; WIDTH_BYTEENA                      ; 1                            ; Untyped                                                   ;
; OPERATION_MODE                     ; ROM                          ; Untyped                                                   ;
; WIDTH_A                            ; 14                           ; Signed Integer                                            ;
; WIDTHAD_A                          ; 6                            ; Signed Integer                                            ;
; NUMWORDS_A                         ; 64                           ; Signed Integer                                            ;
; OUTDATA_REG_A                      ; UNREGISTERED                 ; Untyped                                                   ;
; ADDRESS_ACLR_A                     ; NONE                         ; Untyped                                                   ;
; OUTDATA_ACLR_A                     ; NONE                         ; Untyped                                                   ;
; WRCONTROL_ACLR_A                   ; NONE                         ; Untyped                                                   ;
; INDATA_ACLR_A                      ; NONE                         ; Untyped                                                   ;
; BYTEENA_ACLR_A                     ; NONE                         ; Untyped                                                   ;
; WIDTH_B                            ; 1                            ; Untyped                                                   ;
; WIDTHAD_B                          ; 1                            ; Untyped                                                   ;
; NUMWORDS_B                         ; 1                            ; Untyped                                                   ;
; INDATA_REG_B                       ; CLOCK1                       ; Untyped                                                   ;
; WRCONTROL_WRADDRESS_REG_B          ; CLOCK1                       ; Untyped                                                   ;
; RDCONTROL_REG_B                    ; CLOCK1                       ; Untyped                                                   ;
; ADDRESS_REG_B                      ; CLOCK1                       ; Untyped                                                   ;
; OUTDATA_REG_B                      ; UNREGISTERED                 ; Untyped                                                   ;
; BYTEENA_REG_B                      ; CLOCK1                       ; Untyped                                                   ;
; INDATA_ACLR_B                      ; NONE                         ; Untyped                                                   ;
; WRCONTROL_ACLR_B                   ; NONE                         ; Untyped                                                   ;
; ADDRESS_ACLR_B                     ; NONE                         ; Untyped                                                   ;
; OUTDATA_ACLR_B                     ; NONE                         ; Untyped                                                   ;
; RDCONTROL_ACLR_B                   ; NONE                         ; Untyped                                                   ;
; BYTEENA_ACLR_B                     ; NONE                         ; Untyped                                                   ;
; WIDTH_BYTEENA_A                    ; 1                            ; Signed Integer                                            ;
; WIDTH_BYTEENA_B                    ; 1                            ; Untyped                                                   ;
; RAM_BLOCK_TYPE                     ; AUTO                         ; Untyped                                                   ;
; BYTE_SIZE                          ; 8                            ; Untyped                                                   ;
; READ_DURING_WRITE_MODE_MIXED_PORTS ; DONT_CARE                    ; Untyped                                                   ;
; READ_DURING_WRITE_MODE_PORT_A      ; NEW_DATA_NO_NBE_READ         ; Untyped                                                   ;
; READ_DURING_WRITE_MODE_PORT_B      ; NEW_DATA_NO_NBE_READ         ; Untyped                                                   ;
; INIT_FILE                          ; ../script/sawtooth_64x14.mif ; Untyped                                                   ;
; INIT_FILE_LAYOUT                   ; PORT_A                       ; Untyped                                                   ;
; MAXIMUM_DEPTH                      ; 0                            ; Untyped                                                   ;
; CLOCK_ENABLE_INPUT_A               ; BYPASS                       ; Untyped                                                   ;
; CLOCK_ENABLE_INPUT_B               ; NORMAL                       ; Untyped                                                   ;
; CLOCK_ENABLE_OUTPUT_A              ; BYPASS                       ; Untyped                                                   ;
; CLOCK_ENABLE_OUTPUT_B              ; NORMAL                       ; Untyped                                                   ;
; CLOCK_ENABLE_CORE_A                ; USE_INPUT_CLKEN              ; Untyped                                                   ;
; CLOCK_ENABLE_CORE_B                ; USE_INPUT_CLKEN              ; Untyped                                                   ;
; ENABLE_ECC                         ; FALSE                        ; Untyped                                                   ;
; ECC_PIPELINE_STAGE_ENABLED         ; FALSE                        ; Untyped                                                   ;
; WIDTH_ECCSTATUS                    ; 3                            ; Untyped                                                   ;
; DEVICE_FAMILY                      ; Cyclone IV E                 ; Untyped                                                   ;
; CBXI_PARAMETER                     ; altsyncram_rdb1              ; Untyped                                                   ;
+------------------------------------+------------------------------+-----------------------------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+----------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: VOLTAGE_SCALER_CLOCKED:inst11 ;
+-----------------+-------+--------------------------------------------------+
; Parameter Name  ; Value ; Type                                             ;
+-----------------+-------+--------------------------------------------------+
; ROM_MAX         ; 16383 ; Signed Integer                                   ;
; DEFAULT_PEAK_MV ; 3080  ; Signed Integer                                   ;
; HALF_ROM_MAX    ; 8191  ; Signed Integer                                   ;
+-----------------+-------+--------------------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+----------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: VOLTAGE_SCALER_CLOCKED:inst12 ;
+-----------------+-------+--------------------------------------------------+
; Parameter Name  ; Value ; Type                                             ;
+-----------------+-------+--------------------------------------------------+
; ROM_MAX         ; 16383 ; Signed Integer                                   ;
; DEFAULT_PEAK_MV ; 3080  ; Signed Integer                                   ;
; HALF_ROM_MAX    ; 8191  ; Signed Integer                                   ;
+-----------------+-------+--------------------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+----------------------------------------------------------------------------------------------------+
; Parameter Settings for Inferred Entity Instance: voltage_scaler_clocked_fast:inst10|lpm_mult:Mult1 ;
+------------------------------------------------+--------------+------------------------------------+
; Parameter Name                                 ; Value        ; Type                               ;
+------------------------------------------------+--------------+------------------------------------+
; AUTO_CARRY_CHAINS                              ; ON           ; AUTO_CARRY                         ;
; IGNORE_CARRY_BUFFERS                           ; OFF          ; IGNORE_CARRY                       ;
; AUTO_CASCADE_CHAINS                            ; ON           ; AUTO_CASCADE                       ;
; IGNORE_CASCADE_BUFFERS                         ; OFF          ; IGNORE_CASCADE                     ;
; LPM_WIDTHA                                     ; 26           ; Untyped                            ;
; LPM_WIDTHB                                     ; 7            ; Untyped                            ;
; LPM_WIDTHP                                     ; 33           ; Untyped                            ;
; LPM_WIDTHR                                     ; 33           ; Untyped                            ;
; LPM_WIDTHS                                     ; 1            ; Untyped                            ;
; LPM_REPRESENTATION                             ; UNSIGNED     ; Untyped                            ;
; LPM_PIPELINE                                   ; 0            ; Untyped                            ;
; LATENCY                                        ; 0            ; Untyped                            ;
; INPUT_A_IS_CONSTANT                            ; NO           ; Untyped                            ;
; INPUT_B_IS_CONSTANT                            ; YES          ; Untyped                            ;
; USE_EAB                                        ; OFF          ; Untyped                            ;
; MAXIMIZE_SPEED                                 ; 5            ; Untyped                            ;
; DEVICE_FAMILY                                  ; Cyclone IV E ; Untyped                            ;
; CARRY_CHAIN                                    ; MANUAL       ; Untyped                            ;
; APEX20K_TECHNOLOGY_MAPPER                      ; LUT          ; TECH_MAPPER_APEX20K                ;
; DEDICATED_MULTIPLIER_CIRCUITRY                 ; AUTO         ; Untyped                            ;
; DEDICATED_MULTIPLIER_MIN_INPUT_WIDTH_FOR_AUTO  ; 0            ; Untyped                            ;
; DEDICATED_MULTIPLIER_MIN_OUTPUT_WIDTH_FOR_AUTO ; 0            ; Untyped                            ;
; CBXI_PARAMETER                                 ; mult_cft     ; Untyped                            ;
; INPUT_A_FIXED_VALUE                            ; Bx           ; Untyped                            ;
; INPUT_B_FIXED_VALUE                            ; Bx           ; Untyped                            ;
; USE_AHDL_IMPLEMENTATION                        ; OFF          ; Untyped                            ;
+------------------------------------------------+--------------+------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+----------------------------------------------------------------------------------------------------+
; Parameter Settings for Inferred Entity Instance: voltage_scaler_clocked_fast:inst14|lpm_mult:Mult1 ;
+------------------------------------------------+--------------+------------------------------------+
; Parameter Name                                 ; Value        ; Type                               ;
+------------------------------------------------+--------------+------------------------------------+
; AUTO_CARRY_CHAINS                              ; ON           ; AUTO_CARRY                         ;
; IGNORE_CARRY_BUFFERS                           ; OFF          ; IGNORE_CARRY                       ;
; AUTO_CASCADE_CHAINS                            ; ON           ; AUTO_CASCADE                       ;
; IGNORE_CASCADE_BUFFERS                         ; OFF          ; IGNORE_CASCADE                     ;
; LPM_WIDTHA                                     ; 26           ; Untyped                            ;
; LPM_WIDTHB                                     ; 7            ; Untyped                            ;
; LPM_WIDTHP                                     ; 33           ; Untyped                            ;
; LPM_WIDTHR                                     ; 33           ; Untyped                            ;
; LPM_WIDTHS                                     ; 1            ; Untyped                            ;
; LPM_REPRESENTATION                             ; UNSIGNED     ; Untyped                            ;
; LPM_PIPELINE                                   ; 0            ; Untyped                            ;
; LATENCY                                        ; 0            ; Untyped                            ;
; INPUT_A_IS_CONSTANT                            ; NO           ; Untyped                            ;
; INPUT_B_IS_CONSTANT                            ; YES          ; Untyped                            ;
; USE_EAB                                        ; OFF          ; Untyped                            ;
; MAXIMIZE_SPEED                                 ; 5            ; Untyped                            ;
; DEVICE_FAMILY                                  ; Cyclone IV E ; Untyped                            ;
; CARRY_CHAIN                                    ; MANUAL       ; Untyped                            ;
; APEX20K_TECHNOLOGY_MAPPER                      ; LUT          ; TECH_MAPPER_APEX20K                ;
; DEDICATED_MULTIPLIER_CIRCUITRY                 ; AUTO         ; Untyped                            ;
; DEDICATED_MULTIPLIER_MIN_INPUT_WIDTH_FOR_AUTO  ; 0            ; Untyped                            ;
; DEDICATED_MULTIPLIER_MIN_OUTPUT_WIDTH_FOR_AUTO ; 0            ; Untyped                            ;
; CBXI_PARAMETER                                 ; mult_cft     ; Untyped                            ;
; INPUT_A_FIXED_VALUE                            ; Bx           ; Untyped                            ;
; INPUT_B_FIXED_VALUE                            ; Bx           ; Untyped                            ;
; USE_AHDL_IMPLEMENTATION                        ; OFF          ; Untyped                            ;
+------------------------------------------------+--------------+------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+----------------------------------------------------------------------------------------------------+
; Parameter Settings for Inferred Entity Instance: voltage_scaler_clocked_fast:inst10|lpm_mult:Mult0 ;
+------------------------------------------------+--------------+------------------------------------+
; Parameter Name                                 ; Value        ; Type                               ;
+------------------------------------------------+--------------+------------------------------------+
; AUTO_CARRY_CHAINS                              ; ON           ; AUTO_CARRY                         ;
; IGNORE_CARRY_BUFFERS                           ; OFF          ; IGNORE_CARRY                       ;
; AUTO_CASCADE_CHAINS                            ; ON           ; AUTO_CASCADE                       ;
; IGNORE_CASCADE_BUFFERS                         ; OFF          ; IGNORE_CASCADE                     ;
; LPM_WIDTHA                                     ; 14           ; Untyped                            ;
; LPM_WIDTHB                                     ; 12           ; Untyped                            ;
; LPM_WIDTHP                                     ; 26           ; Untyped                            ;
; LPM_WIDTHR                                     ; 26           ; Untyped                            ;
; LPM_WIDTHS                                     ; 1            ; Untyped                            ;
; LPM_REPRESENTATION                             ; UNSIGNED     ; Untyped                            ;
; LPM_PIPELINE                                   ; 0            ; Untyped                            ;
; LATENCY                                        ; 0            ; Untyped                            ;
; INPUT_A_IS_CONSTANT                            ; NO           ; Untyped                            ;
; INPUT_B_IS_CONSTANT                            ; NO           ; Untyped                            ;
; USE_EAB                                        ; OFF          ; Untyped                            ;
; MAXIMIZE_SPEED                                 ; 5            ; Untyped                            ;
; DEVICE_FAMILY                                  ; Cyclone IV E ; Untyped                            ;
; CARRY_CHAIN                                    ; MANUAL       ; Untyped                            ;
; APEX20K_TECHNOLOGY_MAPPER                      ; LUT          ; TECH_MAPPER_APEX20K                ;
; DEDICATED_MULTIPLIER_CIRCUITRY                 ; AUTO         ; Untyped                            ;
; DEDICATED_MULTIPLIER_MIN_INPUT_WIDTH_FOR_AUTO  ; 0            ; Untyped                            ;
; DEDICATED_MULTIPLIER_MIN_OUTPUT_WIDTH_FOR_AUTO ; 0            ; Untyped                            ;
; CBXI_PARAMETER                                 ; mult_4dt     ; Untyped                            ;
; INPUT_A_FIXED_VALUE                            ; Bx           ; Untyped                            ;
; INPUT_B_FIXED_VALUE                            ; Bx           ; Untyped                            ;
; USE_AHDL_IMPLEMENTATION                        ; OFF          ; Untyped                            ;
+------------------------------------------------+--------------+------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+----------------------------------------------------------------------------------------------------+
; Parameter Settings for Inferred Entity Instance: voltage_scaler_clocked_fast:inst14|lpm_mult:Mult0 ;
+------------------------------------------------+--------------+------------------------------------+
; Parameter Name                                 ; Value        ; Type                               ;
+------------------------------------------------+--------------+------------------------------------+
; AUTO_CARRY_CHAINS                              ; ON           ; AUTO_CARRY                         ;
; IGNORE_CARRY_BUFFERS                           ; OFF          ; IGNORE_CARRY                       ;
; AUTO_CASCADE_CHAINS                            ; ON           ; AUTO_CASCADE                       ;
; IGNORE_CASCADE_BUFFERS                         ; OFF          ; IGNORE_CASCADE                     ;
; LPM_WIDTHA                                     ; 14           ; Untyped                            ;
; LPM_WIDTHB                                     ; 12           ; Untyped                            ;
; LPM_WIDTHP                                     ; 26           ; Untyped                            ;
; LPM_WIDTHR                                     ; 26           ; Untyped                            ;
; LPM_WIDTHS                                     ; 1            ; Untyped                            ;
; LPM_REPRESENTATION                             ; UNSIGNED     ; Untyped                            ;
; LPM_PIPELINE                                   ; 0            ; Untyped                            ;
; LATENCY                                        ; 0            ; Untyped                            ;
; INPUT_A_IS_CONSTANT                            ; NO           ; Untyped                            ;
; INPUT_B_IS_CONSTANT                            ; NO           ; Untyped                            ;
; USE_EAB                                        ; OFF          ; Untyped                            ;
; MAXIMIZE_SPEED                                 ; 5            ; Untyped                            ;
; DEVICE_FAMILY                                  ; Cyclone IV E ; Untyped                            ;
; CARRY_CHAIN                                    ; MANUAL       ; Untyped                            ;
; APEX20K_TECHNOLOGY_MAPPER                      ; LUT          ; TECH_MAPPER_APEX20K                ;
; DEDICATED_MULTIPLIER_CIRCUITRY                 ; AUTO         ; Untyped                            ;
; DEDICATED_MULTIPLIER_MIN_INPUT_WIDTH_FOR_AUTO  ; 0            ; Untyped                            ;
; DEDICATED_MULTIPLIER_MIN_OUTPUT_WIDTH_FOR_AUTO ; 0            ; Untyped                            ;
; CBXI_PARAMETER                                 ; mult_4dt     ; Untyped                            ;
; INPUT_A_FIXED_VALUE                            ; Bx           ; Untyped                            ;
; INPUT_B_FIXED_VALUE                            ; Bx           ; Untyped                            ;
; USE_AHDL_IMPLEMENTATION                        ; OFF          ; Untyped                            ;
+------------------------------------------------+--------------+------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+---------------------------------------------------------------------+
; altpll Parameter Settings by Entity Instance                        ;
+-------------------------------+-------------------------------------+
; Name                          ; Value                               ;
+-------------------------------+-------------------------------------+
; Number of entity instances    ; 1                                   ;
; Entity Instance               ; MYPLL:inst6|altpll:altpll_component ;
;     -- OPERATION_MODE         ; NORMAL                              ;
;     -- PLL_TYPE               ; AUTO                                ;
;     -- PRIMARY_CLOCK          ; INCLK0                              ;
;     -- INCLK0_INPUT_FREQUENCY ; 20000                               ;
;     -- INCLK1_INPUT_FREQUENCY ; 0                                   ;
;     -- VCO_MULTIPLY_BY        ; 0                                   ;
;     -- VCO_DIVIDE_BY          ; 0                                   ;
+-------------------------------+-------------------------------------+


+------------------------------------------------------------------------+
; dcfifo Parameter Settings by Entity Instance                           ;
+----------------------------+-------------------------------------------+
; Name                       ; Value                                     ;
+----------------------------+-------------------------------------------+
; Number of entity instances ; 2                                         ;
; Entity Instance            ; TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component ;
;     -- FIFO Type           ; Dual Clock                                ;
;     -- LPM_WIDTH           ; 12                                        ;
;     -- LPM_NUMWORDS        ; 1024                                      ;
;     -- LPM_SHOWAHEAD       ; OFF                                       ;
;     -- USE_EAB             ; ON                                        ;
; Entity Instance            ; TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component ;
;     -- FIFO Type           ; Dual Clock                                ;
;     -- LPM_WIDTH           ; 12                                        ;
;     -- LPM_NUMWORDS        ; 1024                                      ;
;     -- LPM_SHOWAHEAD       ; OFF                                       ;
;     -- USE_EAB             ; ON                                        ;
+----------------------------+-------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------------+
; altsyncram Parameter Settings by Entity Instance                                                                             ;
+-------------------------------------------+----------------------------------------------------------------------------------+
; Name                                      ; Value                                                                            ;
+-------------------------------------------+----------------------------------------------------------------------------------+
; Number of entity instances                ; 8                                                                                ;
; Entity Instance                           ; DA_WAVEFORM:inst5|sin_rom:sin_rom_inst|altsyncram:altsyncram_component           ;
;     -- OPERATION_MODE                     ; ROM                                                                              ;
;     -- WIDTH_A                            ; 14                                                                               ;
;     -- NUMWORDS_A                         ; 64                                                                               ;
;     -- OUTDATA_REG_A                      ; UNREGISTERED                                                                     ;
;     -- WIDTH_B                            ; 1                                                                                ;
;     -- NUMWORDS_B                         ; 1                                                                                ;
;     -- ADDRESS_REG_B                      ; CLOCK1                                                                           ;
;     -- OUTDATA_REG_B                      ; UNREGISTERED                                                                     ;
;     -- RAM_BLOCK_TYPE                     ; AUTO                                                                             ;
;     -- READ_DURING_WRITE_MODE_MIXED_PORTS ; DONT_CARE                                                                        ;
; Entity Instance                           ; DA_WAVEFORM:inst5|sqaure_rom:sqaure_rom_inst|altsyncram:altsyncram_component     ;
;     -- OPERATION_MODE                     ; ROM                                                                              ;
;     -- WIDTH_A                            ; 14                                                                               ;
;     -- NUMWORDS_A                         ; 64                                                                               ;
;     -- OUTDATA_REG_A                      ; UNREGISTERED                                                                     ;
;     -- WIDTH_B                            ; 1                                                                                ;
;     -- NUMWORDS_B                         ; 1                                                                                ;
;     -- ADDRESS_REG_B                      ; CLOCK1                                                                           ;
;     -- OUTDATA_REG_B                      ; UNREGISTERED                                                                     ;
;     -- RAM_BLOCK_TYPE                     ; AUTO                                                                             ;
;     -- READ_DURING_WRITE_MODE_MIXED_PORTS ; DONT_CARE                                                                        ;
; Entity Instance                           ; DA_WAVEFORM:inst5|triangle_rom:triangle_rom_inst|altsyncram:altsyncram_component ;
;     -- OPERATION_MODE                     ; ROM                                                                              ;
;     -- WIDTH_A                            ; 14                                                                               ;
;     -- NUMWORDS_A                         ; 64                                                                               ;
;     -- OUTDATA_REG_A                      ; UNREGISTERED                                                                     ;
;     -- WIDTH_B                            ; 1                                                                                ;
;     -- NUMWORDS_B                         ; 1                                                                                ;
;     -- ADDRESS_REG_B                      ; CLOCK1                                                                           ;
;     -- OUTDATA_REG_B                      ; UNREGISTERED                                                                     ;
;     -- RAM_BLOCK_TYPE                     ; AUTO                                                                             ;
;     -- READ_DURING_WRITE_MODE_MIXED_PORTS ; DONT_CARE                                                                        ;
; Entity Instance                           ; DA_WAVEFORM:inst5|sawtooth_rom:sawtooth_rom_inst|altsyncram:altsyncram_component ;
;     -- OPERATION_MODE                     ; ROM                                                                              ;
;     -- WIDTH_A                            ; 14                                                                               ;
;     -- NUMWORDS_A                         ; 64                                                                               ;
;     -- OUTDATA_REG_A                      ; UNREGISTERED                                                                     ;
;     -- WIDTH_B                            ; 1                                                                                ;
;     -- NUMWORDS_B                         ; 1                                                                                ;
;     -- ADDRESS_REG_B                      ; CLOCK1                                                                           ;
;     -- OUTDATA_REG_B                      ; UNREGISTERED                                                                     ;
;     -- RAM_BLOCK_TYPE                     ; AUTO                                                                             ;
;     -- READ_DURING_WRITE_MODE_MIXED_PORTS ; DONT_CARE                                                                        ;
; Entity Instance                           ; DA_WAVEFORM:inst8|sin_rom:sin_rom_inst|altsyncram:altsyncram_component           ;
;     -- OPERATION_MODE                     ; ROM                                                                              ;
;     -- WIDTH_A                            ; 14                                                                               ;
;     -- NUMWORDS_A                         ; 64                                                                               ;
;     -- OUTDATA_REG_A                      ; UNREGISTERED                                                                     ;
;     -- WIDTH_B                            ; 1                                                                                ;
;     -- NUMWORDS_B                         ; 1                                                                                ;
;     -- ADDRESS_REG_B                      ; CLOCK1                                                                           ;
;     -- OUTDATA_REG_B                      ; UNREGISTERED                                                                     ;
;     -- RAM_BLOCK_TYPE                     ; AUTO                                                                             ;
;     -- READ_DURING_WRITE_MODE_MIXED_PORTS ; DONT_CARE                                                                        ;
; Entity Instance                           ; DA_WAVEFORM:inst8|sqaure_rom:sqaure_rom_inst|altsyncram:altsyncram_component     ;
;     -- OPERATION_MODE                     ; ROM                                                                              ;
;     -- WIDTH_A                            ; 14                                                                               ;
;     -- NUMWORDS_A                         ; 64                                                                               ;
;     -- OUTDATA_REG_A                      ; UNREGISTERED                                                                     ;
;     -- WIDTH_B                            ; 1                                                                                ;
;     -- NUMWORDS_B                         ; 1                                                                                ;
;     -- ADDRESS_REG_B                      ; CLOCK1                                                                           ;
;     -- OUTDATA_REG_B                      ; UNREGISTERED                                                                     ;
;     -- RAM_BLOCK_TYPE                     ; AUTO                                                                             ;
;     -- READ_DURING_WRITE_MODE_MIXED_PORTS ; DONT_CARE                                                                        ;
; Entity Instance                           ; DA_WAVEFORM:inst8|triangle_rom:triangle_rom_inst|altsyncram:altsyncram_component ;
;     -- OPERATION_MODE                     ; ROM                                                                              ;
;     -- WIDTH_A                            ; 14                                                                               ;
;     -- NUMWORDS_A                         ; 64                                                                               ;
;     -- OUTDATA_REG_A                      ; UNREGISTERED                                                                     ;
;     -- WIDTH_B                            ; 1                                                                                ;
;     -- NUMWORDS_B                         ; 1                                                                                ;
;     -- ADDRESS_REG_B                      ; CLOCK1                                                                           ;
;     -- OUTDATA_REG_B                      ; UNREGISTERED                                                                     ;
;     -- RAM_BLOCK_TYPE                     ; AUTO                                                                             ;
;     -- READ_DURING_WRITE_MODE_MIXED_PORTS ; DONT_CARE                                                                        ;
; Entity Instance                           ; DA_WAVEFORM:inst8|sawtooth_rom:sawtooth_rom_inst|altsyncram:altsyncram_component ;
;     -- OPERATION_MODE                     ; ROM                                                                              ;
;     -- WIDTH_A                            ; 14                                                                               ;
;     -- NUMWORDS_A                         ; 64                                                                               ;
;     -- OUTDATA_REG_A                      ; UNREGISTERED                                                                     ;
;     -- WIDTH_B                            ; 1                                                                                ;
;     -- NUMWORDS_B                         ; 1                                                                                ;
;     -- ADDRESS_REG_B                      ; CLOCK1                                                                           ;
;     -- OUTDATA_REG_B                      ; UNREGISTERED                                                                     ;
;     -- RAM_BLOCK_TYPE                     ; AUTO                                                                             ;
;     -- READ_DURING_WRITE_MODE_MIXED_PORTS ; DONT_CARE                                                                        ;
+-------------------------------------------+----------------------------------------------------------------------------------+


+-------------------------------------------------------------------------------------------+
; lpm_mult Parameter Settings by Entity Instance                                            ;
+---------------------------------------+---------------------------------------------------+
; Name                                  ; Value                                             ;
+---------------------------------------+---------------------------------------------------+
; Number of entity instances            ; 4                                                 ;
; Entity Instance                       ; voltage_scaler_clocked_fast:inst10|lpm_mult:Mult1 ;
;     -- LPM_WIDTHA                     ; 26                                                ;
;     -- LPM_WIDTHB                     ; 7                                                 ;
;     -- LPM_WIDTHP                     ; 33                                                ;
;     -- LPM_REPRESENTATION             ; UNSIGNED                                          ;
;     -- INPUT_A_IS_CONSTANT            ; NO                                                ;
;     -- INPUT_B_IS_CONSTANT            ; YES                                               ;
;     -- USE_EAB                        ; OFF                                               ;
;     -- DEDICATED_MULTIPLIER_CIRCUITRY ; AUTO                                              ;
;     -- INPUT_A_FIXED_VALUE            ; Bx                                                ;
;     -- INPUT_B_FIXED_VALUE            ; Bx                                                ;
; Entity Instance                       ; voltage_scaler_clocked_fast:inst14|lpm_mult:Mult1 ;
;     -- LPM_WIDTHA                     ; 26                                                ;
;     -- LPM_WIDTHB                     ; 7                                                 ;
;     -- LPM_WIDTHP                     ; 33                                                ;
;     -- LPM_REPRESENTATION             ; UNSIGNED                                          ;
;     -- INPUT_A_IS_CONSTANT            ; NO                                                ;
;     -- INPUT_B_IS_CONSTANT            ; YES                                               ;
;     -- USE_EAB                        ; OFF                                               ;
;     -- DEDICATED_MULTIPLIER_CIRCUITRY ; AUTO                                              ;
;     -- INPUT_A_FIXED_VALUE            ; Bx                                                ;
;     -- INPUT_B_FIXED_VALUE            ; Bx                                                ;
; Entity Instance                       ; voltage_scaler_clocked_fast:inst10|lpm_mult:Mult0 ;
;     -- LPM_WIDTHA                     ; 14                                                ;
;     -- LPM_WIDTHB                     ; 12                                                ;
;     -- LPM_WIDTHP                     ; 26                                                ;
;     -- LPM_REPRESENTATION             ; UNSIGNED                                          ;
;     -- INPUT_A_IS_CONSTANT            ; NO                                                ;
;     -- INPUT_B_IS_CONSTANT            ; NO                                                ;
;     -- USE_EAB                        ; OFF                                               ;
;     -- DEDICATED_MULTIPLIER_CIRCUITRY ; AUTO                                              ;
;     -- INPUT_A_FIXED_VALUE            ; Bx                                                ;
;     -- INPUT_B_FIXED_VALUE            ; Bx                                                ;
; Entity Instance                       ; voltage_scaler_clocked_fast:inst14|lpm_mult:Mult0 ;
;     -- LPM_WIDTHA                     ; 14                                                ;
;     -- LPM_WIDTHB                     ; 12                                                ;
;     -- LPM_WIDTHP                     ; 26                                                ;
;     -- LPM_REPRESENTATION             ; UNSIGNED                                          ;
;     -- INPUT_A_IS_CONSTANT            ; NO                                                ;
;     -- INPUT_B_IS_CONSTANT            ; NO                                                ;
;     -- USE_EAB                        ; OFF                                               ;
;     -- DEDICATED_MULTIPLIER_CIRCUITRY ; AUTO                                              ;
;     -- INPUT_A_FIXED_VALUE            ; Bx                                                ;
;     -- INPUT_B_FIXED_VALUE            ; Bx                                                ;
+---------------------------------------+---------------------------------------------------+


+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Port Connectivity Checks: "DA_WAVEFORM:inst5|sawtooth_rom:sawtooth_rom_inst"                                                                                                                                  ;
+---------+-------+----------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Port    ; Type  ; Severity ; Details                                                                                                                                                                          ;
+---------+-------+----------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; address ; Input ; Warning  ; Input port expression (7 bits) is wider than the input port (6 bits) it drives.  The 1 most-significant bit(s) in the expression will be dangling if they have no other fanouts. ;
+---------+-------+----------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+


+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Port Connectivity Checks: "DA_WAVEFORM:inst5|triangle_rom:triangle_rom_inst"                                                                                                                                  ;
+---------+-------+----------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Port    ; Type  ; Severity ; Details                                                                                                                                                                          ;
+---------+-------+----------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; address ; Input ; Warning  ; Input port expression (7 bits) is wider than the input port (6 bits) it drives.  The 1 most-significant bit(s) in the expression will be dangling if they have no other fanouts. ;
+---------+-------+----------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+


+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Port Connectivity Checks: "DA_WAVEFORM:inst5|sqaure_rom:sqaure_rom_inst"                                                                                                                                      ;
+---------+-------+----------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Port    ; Type  ; Severity ; Details                                                                                                                                                                          ;
+---------+-------+----------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; address ; Input ; Warning  ; Input port expression (7 bits) is wider than the input port (6 bits) it drives.  The 1 most-significant bit(s) in the expression will be dangling if they have no other fanouts. ;
+---------+-------+----------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+


+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Port Connectivity Checks: "DA_WAVEFORM:inst5|sin_rom:sin_rom_inst"                                                                                                                                            ;
+---------+-------+----------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Port    ; Type  ; Severity ; Details                                                                                                                                                                          ;
+---------+-------+----------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; address ; Input ; Warning  ; Input port expression (7 bits) is wider than the input port (6 bits) it drives.  The 1 most-significant bit(s) in the expression will be dangling if they have no other fanouts. ;
+---------+-------+----------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+


+-----------------------------------------------------+
; Post-Synthesis Netlist Statistics for Top Partition ;
+-----------------------+-----------------------------+
; Type                  ; Count                       ;
+-----------------------+-----------------------------+
; boundary_port         ; 80                          ;
; cycloneiii_ff         ; 1106                        ;
;     CLR SLD           ; 12                          ;
;     ENA               ; 144                         ;
;     ENA CLR           ; 344                         ;
;     ENA CLR SLD       ; 12                          ;
;     SCLR              ; 65                          ;
;     SLD               ; 56                          ;
;     plain             ; 473                         ;
; cycloneiii_io_obuf    ; 16                          ;
; cycloneiii_lcell_comb ; 1331                        ;
;     arith             ; 429                         ;
;         2 data inputs ; 190                         ;
;         3 data inputs ; 239                         ;
;     normal            ; 902                         ;
;         1 data inputs ; 74                          ;
;         2 data inputs ; 44                          ;
;         3 data inputs ; 432                         ;
;         4 data inputs ; 352                         ;
; cycloneiii_mac_mult   ; 6                           ;
; cycloneiii_mac_out    ; 6                           ;
; cycloneiii_pll        ; 1                           ;
; cycloneiii_ram_block  ; 136                         ;
;                       ;                             ;
; Max LUT depth         ; 8.10                        ;
; Average LUT depth     ; 2.89                        ;
+-----------------------+-----------------------------+


+-------------------------------+
; Elapsed Time Per Partition    ;
+----------------+--------------+
; Partition Name ; Elapsed Time ;
+----------------+--------------+
; Top            ; 00:00:01     ;
+----------------+--------------+


+-------------------------------+
; Analysis & Synthesis Messages ;
+-------------------------------+
Info: *******************************************************************
Info: Running Quartus Prime Analysis & Synthesis
    Info: Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition
    Info: Processing started: Fri Aug 01 09:24:01 2025
Info: Command: quartus_map --read_settings_files=on --write_settings_files=off ZUOLAN_FPGA_OBJECT -c ZUOLAN_FPGA_OBJECT
Info (20030): Parallel compilation is enabled and will use 14 of the 14 processors detected
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/src/voltage_scaler_clocked_fast.v
    Info (12023): Found entity 1: voltage_scaler_clocked_fast File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/voltage_scaler_clocked_fast.v Line: 1
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/ip/tyfifo/tyfifo.v
    Info (12023): Found entity 1: TYFIFO File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/TYFIFO/TYFIFO.v Line: 39
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/ip/triangle_rom/triangle_rom.v
    Info (12023): Found entity 1: triangle_rom File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/triangle_rom/triangle_rom.v Line: 39
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/ip/sqaure_rom/sqaure_rom.v
    Info (12023): Found entity 1: sqaure_rom File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/sqaure_rom/sqaure_rom.v Line: 39
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/ip/sawtooth_rom/sawtooth_rom.v
    Info (12023): Found entity 1: sawtooth_rom File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/sawtooth_rom/sawtooth_rom.v Line: 39
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/ip/sin_rom/sin_rom.v
    Info (12023): Found entity 1: sin_rom File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/sin_rom/sin_rom.v Line: 39
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/ip/mypll/mypll.v
    Info (12023): Found entity 1: MYPLL File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/MYPLL/MYPLL.v Line: 39
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/src/voltage_scaler_clocked.v
    Info (12023): Found entity 1: VOLTAGE_SCALER_CLOCKED File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/VOLTAGE_SCALER_CLOCKED.v Line: 13
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/src/test.v
    Info (12023): Found entity 1: test File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/test.v Line: 12
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/src/master_ctrl.v
    Info (12023): Found entity 1: MASTER_CTRL File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/MASTER_CTRL.v Line: 21
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/src/freq_dev.v
    Info (12023): Found entity 1: FREQ_DEV File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/FREQ_DEV.v Line: 13
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/src/fmc_control.v
    Info (12023): Found entity 1: FMC_CONTROL File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/FMC_CONTROL.v Line: 15
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/src/da_waveform.v
    Info (12023): Found entity 1: DA_WAVEFORM File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_WAVEFORM.v Line: 15
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/src/da_parameter_deal.v
    Info (12023): Found entity 1: DA_PARAMETER_DEAL File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 1
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/src/da_parameter_ctrl.v
    Info (12023): Found entity 1: DA_PARAMETER_CTRL File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_CTRL.v Line: 34
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/src/cnt32.v
    Info (12023): Found entity 1: CNT32 File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/CNT32.v Line: 11
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/src/ad_freq_word.v
    Info (12023): Found entity 1: AD_FREQ_WORD File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 19
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/src/ad_freq_measure.v
    Info (12023): Found entity 1: AD_FREQ_MEASURE File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 12
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1 (1)/zuolan_fpga/src/ad_data_deal.v
    Info (12023): Found entity 1: AD_DATA_DEAL File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 12
Info (12021): Found 1 design units, including 1 entities, in source file top.bdf
    Info (12023): Found entity 1: TOP
Info (12127): Elaborating entity "TOP" for the top level hierarchy
Warning (275013): Port "clk" of type VOLTAGE_SCALER_CLOCKED and instance "inst11" is missing source signal
Warning (275013): Port "clk" of type VOLTAGE_SCALER_CLOCKED and instance "inst12" is missing source signal
Info (12128): Elaborating entity "DA_PARAMETER_CTRL" for hierarchy "DA_PARAMETER_CTRL:inst7"
Warning (10230): Verilog HDL assignment warning at DA_PARAMETER_CTRL.v(143): truncated value with size 16 to match size of target (8) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_CTRL.v Line: 143
Warning (10230): Verilog HDL assignment warning at DA_PARAMETER_CTRL.v(145): truncated value with size 32 to match size of target (8) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_CTRL.v Line: 145
Warning (10230): Verilog HDL assignment warning at DA_PARAMETER_CTRL.v(148): truncated value with size 32 to match size of target (8) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_CTRL.v Line: 148
Warning (10230): Verilog HDL assignment warning at DA_PARAMETER_CTRL.v(167): truncated value with size 16 to match size of target (8) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_CTRL.v Line: 167
Warning (10230): Verilog HDL assignment warning at DA_PARAMETER_CTRL.v(169): truncated value with size 32 to match size of target (8) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_CTRL.v Line: 169
Warning (10230): Verilog HDL assignment warning at DA_PARAMETER_CTRL.v(172): truncated value with size 32 to match size of target (8) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_CTRL.v Line: 172
Warning (10230): Verilog HDL assignment warning at DA_PARAMETER_CTRL.v(188): truncated value with size 16 to match size of target (8) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_CTRL.v Line: 188
Warning (10230): Verilog HDL assignment warning at DA_PARAMETER_CTRL.v(192): truncated value with size 16 to match size of target (8) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_CTRL.v Line: 192
Info (12128): Elaborating entity "MYPLL" for hierarchy "MYPLL:inst6"
Info (12128): Elaborating entity "altpll" for hierarchy "MYPLL:inst6|altpll:altpll_component" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/MYPLL/MYPLL.v Line: 90
Info (12130): Elaborated megafunction instantiation "MYPLL:inst6|altpll:altpll_component" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/MYPLL/MYPLL.v Line: 90
Info (12133): Instantiated megafunction "MYPLL:inst6|altpll:altpll_component" with the following parameter: File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/MYPLL/MYPLL.v Line: 90
    Info (12134): Parameter "bandwidth_type" = "AUTO"
    Info (12134): Parameter "clk0_divide_by" = "1"
    Info (12134): Parameter "clk0_duty_cycle" = "50"
    Info (12134): Parameter "clk0_multiply_by" = "3"
    Info (12134): Parameter "clk0_phase_shift" = "0"
    Info (12134): Parameter "compensate_clock" = "CLK0"
    Info (12134): Parameter "inclk0_input_frequency" = "20000"
    Info (12134): Parameter "intended_device_family" = "Cyclone IV E"
    Info (12134): Parameter "lpm_hint" = "CBX_MODULE_PREFIX=MYPLL"
    Info (12134): Parameter "lpm_type" = "altpll"
    Info (12134): Parameter "operation_mode" = "NORMAL"
    Info (12134): Parameter "pll_type" = "AUTO"
    Info (12134): Parameter "port_activeclock" = "PORT_UNUSED"
    Info (12134): Parameter "port_areset" = "PORT_UNUSED"
    Info (12134): Parameter "port_clkbad0" = "PORT_UNUSED"
    Info (12134): Parameter "port_clkbad1" = "PORT_UNUSED"
    Info (12134): Parameter "port_clkloss" = "PORT_UNUSED"
    Info (12134): Parameter "port_clkswitch" = "PORT_UNUSED"
    Info (12134): Parameter "port_configupdate" = "PORT_UNUSED"
    Info (12134): Parameter "port_fbin" = "PORT_UNUSED"
    Info (12134): Parameter "port_inclk0" = "PORT_USED"
    Info (12134): Parameter "port_inclk1" = "PORT_UNUSED"
    Info (12134): Parameter "port_locked" = "PORT_UNUSED"
    Info (12134): Parameter "port_pfdena" = "PORT_UNUSED"
    Info (12134): Parameter "port_phasecounterselect" = "PORT_UNUSED"
    Info (12134): Parameter "port_phasedone" = "PORT_UNUSED"
    Info (12134): Parameter "port_phasestep" = "PORT_UNUSED"
    Info (12134): Parameter "port_phaseupdown" = "PORT_UNUSED"
    Info (12134): Parameter "port_pllena" = "PORT_UNUSED"
    Info (12134): Parameter "port_scanaclr" = "PORT_UNUSED"
    Info (12134): Parameter "port_scanclk" = "PORT_UNUSED"
    Info (12134): Parameter "port_scanclkena" = "PORT_UNUSED"
    Info (12134): Parameter "port_scandata" = "PORT_UNUSED"
    Info (12134): Parameter "port_scandataout" = "PORT_UNUSED"
    Info (12134): Parameter "port_scandone" = "PORT_UNUSED"
    Info (12134): Parameter "port_scanread" = "PORT_UNUSED"
    Info (12134): Parameter "port_scanwrite" = "PORT_UNUSED"
    Info (12134): Parameter "port_clk0" = "PORT_USED"
    Info (12134): Parameter "port_clk1" = "PORT_UNUSED"
    Info (12134): Parameter "port_clk2" = "PORT_UNUSED"
    Info (12134): Parameter "port_clk3" = "PORT_UNUSED"
    Info (12134): Parameter "port_clk4" = "PORT_UNUSED"
    Info (12134): Parameter "port_clk5" = "PORT_UNUSED"
    Info (12134): Parameter "port_clkena0" = "PORT_UNUSED"
    Info (12134): Parameter "port_clkena1" = "PORT_UNUSED"
    Info (12134): Parameter "port_clkena2" = "PORT_UNUSED"
    Info (12134): Parameter "port_clkena3" = "PORT_UNUSED"
    Info (12134): Parameter "port_clkena4" = "PORT_UNUSED"
    Info (12134): Parameter "port_clkena5" = "PORT_UNUSED"
    Info (12134): Parameter "port_extclk0" = "PORT_UNUSED"
    Info (12134): Parameter "port_extclk1" = "PORT_UNUSED"
    Info (12134): Parameter "port_extclk2" = "PORT_UNUSED"
    Info (12134): Parameter "port_extclk3" = "PORT_UNUSED"
    Info (12134): Parameter "width_clock" = "5"
Info (12021): Found 1 design units, including 1 entities, in source file db/mypll_altpll1.v
    Info (12023): Found entity 1: MYPLL_altpll1 File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/mypll_altpll1.v Line: 29
Info (12128): Elaborating entity "MYPLL_altpll1" for hierarchy "MYPLL:inst6|altpll:altpll_component|MYPLL_altpll1:auto_generated" File: g:/altera/18.1/quartus/libraries/megafunctions/altpll.tdf Line: 897
Info (12128): Elaborating entity "MASTER_CTRL" for hierarchy "MASTER_CTRL:inst3"
Warning (10235): Verilog HDL Always Construct warning at MASTER_CTRL.v(40): variable "DATA" is read inside the Always Construct but isn't in the Always Construct's Event Control File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/MASTER_CTRL.v Line: 40
Warning (10240): Verilog HDL Always Construct warning at MASTER_CTRL.v(36): inferring latch(es) for variable "CTRL_DATA", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/MASTER_CTRL.v Line: 36
Info (10041): Inferred latch for "CTRL_DATA[0]" at MASTER_CTRL.v(36) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/MASTER_CTRL.v Line: 36
Info (10041): Inferred latch for "CTRL_DATA[1]" at MASTER_CTRL.v(36) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/MASTER_CTRL.v Line: 36
Info (10041): Inferred latch for "CTRL_DATA[2]" at MASTER_CTRL.v(36) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/MASTER_CTRL.v Line: 36
Info (10041): Inferred latch for "CTRL_DATA[3]" at MASTER_CTRL.v(36) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/MASTER_CTRL.v Line: 36
Info (10041): Inferred latch for "CTRL_DATA[4]" at MASTER_CTRL.v(36) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/MASTER_CTRL.v Line: 36
Info (10041): Inferred latch for "CTRL_DATA[5]" at MASTER_CTRL.v(36) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/MASTER_CTRL.v Line: 36
Info (10041): Inferred latch for "CTRL_DATA[6]" at MASTER_CTRL.v(36) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/MASTER_CTRL.v Line: 36
Info (10041): Inferred latch for "CTRL_DATA[7]" at MASTER_CTRL.v(36) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/MASTER_CTRL.v Line: 36
Info (10041): Inferred latch for "CTRL_DATA[8]" at MASTER_CTRL.v(36) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/MASTER_CTRL.v Line: 36
Info (10041): Inferred latch for "CTRL_DATA[9]" at MASTER_CTRL.v(36) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/MASTER_CTRL.v Line: 36
Info (10041): Inferred latch for "CTRL_DATA[10]" at MASTER_CTRL.v(36) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/MASTER_CTRL.v Line: 36
Info (10041): Inferred latch for "CTRL_DATA[11]" at MASTER_CTRL.v(36) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/MASTER_CTRL.v Line: 36
Info (10041): Inferred latch for "CTRL_DATA[12]" at MASTER_CTRL.v(36) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/MASTER_CTRL.v Line: 36
Info (10041): Inferred latch for "CTRL_DATA[13]" at MASTER_CTRL.v(36) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/MASTER_CTRL.v Line: 36
Info (10041): Inferred latch for "CTRL_DATA[14]" at MASTER_CTRL.v(36) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/MASTER_CTRL.v Line: 36
Info (10041): Inferred latch for "CTRL_DATA[15]" at MASTER_CTRL.v(36) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/MASTER_CTRL.v Line: 36
Info (12128): Elaborating entity "FMC_CONTROL" for hierarchy "FMC_CONTROL:inst"
Info (10041): Inferred latch for "addr[0]" at FMC_CONTROL.v(139) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/FMC_CONTROL.v Line: 139
Info (10041): Inferred latch for "addr[1]" at FMC_CONTROL.v(139) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/FMC_CONTROL.v Line: 139
Info (10041): Inferred latch for "addr[2]" at FMC_CONTROL.v(139) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/FMC_CONTROL.v Line: 139
Info (10041): Inferred latch for "addr[3]" at FMC_CONTROL.v(139) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/FMC_CONTROL.v Line: 139
Info (10041): Inferred latch for "addr[4]" at FMC_CONTROL.v(139) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/FMC_CONTROL.v Line: 139
Info (10041): Inferred latch for "addr[5]" at FMC_CONTROL.v(139) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/FMC_CONTROL.v Line: 139
Info (10041): Inferred latch for "addr[6]" at FMC_CONTROL.v(139) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/FMC_CONTROL.v Line: 139
Info (10041): Inferred latch for "addr[7]" at FMC_CONTROL.v(139) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/FMC_CONTROL.v Line: 139
Info (10041): Inferred latch for "addr[8]" at FMC_CONTROL.v(139) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/FMC_CONTROL.v Line: 139
Info (10041): Inferred latch for "addr[9]" at FMC_CONTROL.v(139) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/FMC_CONTROL.v Line: 139
Info (10041): Inferred latch for "addr[10]" at FMC_CONTROL.v(139) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/FMC_CONTROL.v Line: 139
Info (10041): Inferred latch for "addr[11]" at FMC_CONTROL.v(139) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/FMC_CONTROL.v Line: 139
Info (10041): Inferred latch for "addr[12]" at FMC_CONTROL.v(139) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/FMC_CONTROL.v Line: 139
Info (10041): Inferred latch for "addr[13]" at FMC_CONTROL.v(139) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/FMC_CONTROL.v Line: 139
Info (10041): Inferred latch for "addr[14]" at FMC_CONTROL.v(139) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/FMC_CONTROL.v Line: 139
Info (10041): Inferred latch for "addr[15]" at FMC_CONTROL.v(139) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/FMC_CONTROL.v Line: 139
Info (12128): Elaborating entity "AD_FREQ_MEASURE" for hierarchy "AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"
Warning (10270): Verilog HDL Case Statement warning at AD_FREQ_MEASURE.v(51): incomplete case statement has no default case item File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 51
Warning (10240): Verilog HDL Always Construct warning at AD_FREQ_MEASURE.v(49): inferring latch(es) for variable "BASE1_FREQ_DATA_H", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Warning (10240): Verilog HDL Always Construct warning at AD_FREQ_MEASURE.v(49): inferring latch(es) for variable "BASE1_FREQ_DATA_L", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Warning (10240): Verilog HDL Always Construct warning at AD_FREQ_MEASURE.v(49): inferring latch(es) for variable "BASE2_FREQ_DATA_H", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Warning (10240): Verilog HDL Always Construct warning at AD_FREQ_MEASURE.v(49): inferring latch(es) for variable "BASE2_FREQ_DATA_L", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Warning (10240): Verilog HDL Always Construct warning at AD_FREQ_MEASURE.v(49): inferring latch(es) for variable "AD1_FREQ_DATA_H", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Warning (10240): Verilog HDL Always Construct warning at AD_FREQ_MEASURE.v(49): inferring latch(es) for variable "AD1_FREQ_DATA_L", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Warning (10240): Verilog HDL Always Construct warning at AD_FREQ_MEASURE.v(49): inferring latch(es) for variable "AD2_FREQ_DATA_H", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Warning (10240): Verilog HDL Always Construct warning at AD_FREQ_MEASURE.v(49): inferring latch(es) for variable "AD2_FREQ_DATA_L", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_L[0]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_L[1]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_L[2]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_L[3]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_L[4]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_L[5]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_L[6]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_L[7]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_L[8]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_L[9]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_L[10]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_L[11]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_L[12]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_L[13]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_L[14]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_L[15]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_H[0]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_H[1]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_H[2]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_H[3]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_H[4]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_H[5]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_H[6]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_H[7]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_H[8]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_H[9]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_H[10]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_H[11]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_H[12]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_H[13]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_H[14]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD2_FREQ_DATA_H[15]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_L[0]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_L[1]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_L[2]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_L[3]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_L[4]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_L[5]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_L[6]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_L[7]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_L[8]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_L[9]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_L[10]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_L[11]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_L[12]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_L[13]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_L[14]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_L[15]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_H[0]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_H[1]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_H[2]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_H[3]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_H[4]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_H[5]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_H[6]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_H[7]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_H[8]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_H[9]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_H[10]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_H[11]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_H[12]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_H[13]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_H[14]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "AD1_FREQ_DATA_H[15]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_L[0]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_L[1]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_L[2]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_L[3]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_L[4]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_L[5]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_L[6]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_L[7]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_L[8]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_L[9]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_L[10]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_L[11]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_L[12]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_L[13]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_L[14]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_L[15]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_H[0]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_H[1]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_H[2]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_H[3]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_H[4]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_H[5]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_H[6]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_H[7]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_H[8]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_H[9]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_H[10]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_H[11]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_H[12]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_H[13]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_H[14]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE2_FREQ_DATA_H[15]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_L[0]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_L[1]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_L[2]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_L[3]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_L[4]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_L[5]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_L[6]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_L[7]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_L[8]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_L[9]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_L[10]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_L[11]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_L[12]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_L[13]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_L[14]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_L[15]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_H[0]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_H[1]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_H[2]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_H[3]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_H[4]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_H[5]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_H[6]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_H[7]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_H[8]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_H[9]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_H[10]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_H[11]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_H[12]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_H[13]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_H[14]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (10041): Inferred latch for "BASE1_FREQ_DATA_H[15]" at AD_FREQ_MEASURE.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_MEASURE.v Line: 49
Info (12128): Elaborating entity "CNT32" for hierarchy "CNT32:u_AD1_CNT32"
Info (12128): Elaborating entity "test" for hierarchy "test:inst2"
Info (12128): Elaborating entity "AD_DATA_DEAL" for hierarchy "AD_DATA_DEAL:u_AD_DATA_DEAL"
Warning (10270): Verilog HDL Case Statement warning at AD_DATA_DEAL.v(48): incomplete case statement has no default case item File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 48
Warning (10240): Verilog HDL Always Construct warning at AD_DATA_DEAL.v(46): inferring latch(es) for variable "i", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Warning (10240): Verilog HDL Always Construct warning at AD_DATA_DEAL.v(46): inferring latch(es) for variable "ad1_fifo_recv", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Warning (10240): Verilog HDL Always Construct warning at AD_DATA_DEAL.v(46): inferring latch(es) for variable "AD1_FIFO_DATA_OUT", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Warning (10240): Verilog HDL Always Construct warning at AD_DATA_DEAL.v(46): inferring latch(es) for variable "AD1_FLAG_SHOW", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Warning (10240): Verilog HDL Always Construct warning at AD_DATA_DEAL.v(46): inferring latch(es) for variable "ad2_fifo_recv", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Warning (10240): Verilog HDL Always Construct warning at AD_DATA_DEAL.v(46): inferring latch(es) for variable "AD2_FIFO_DATA_OUT", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Warning (10240): Verilog HDL Always Construct warning at AD_DATA_DEAL.v(46): inferring latch(es) for variable "AD2_FLAG_SHOW", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FLAG_SHOW[0]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FLAG_SHOW[1]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FLAG_SHOW[2]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FLAG_SHOW[3]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FLAG_SHOW[4]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FLAG_SHOW[5]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FLAG_SHOW[6]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FLAG_SHOW[7]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FLAG_SHOW[8]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FLAG_SHOW[9]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FLAG_SHOW[10]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FLAG_SHOW[11]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FLAG_SHOW[12]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FLAG_SHOW[13]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FLAG_SHOW[14]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FLAG_SHOW[15]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FIFO_DATA_OUT[0]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FIFO_DATA_OUT[1]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FIFO_DATA_OUT[2]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FIFO_DATA_OUT[3]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FIFO_DATA_OUT[4]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FIFO_DATA_OUT[5]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FIFO_DATA_OUT[6]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FIFO_DATA_OUT[7]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FIFO_DATA_OUT[8]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FIFO_DATA_OUT[9]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FIFO_DATA_OUT[10]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FIFO_DATA_OUT[11]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FIFO_DATA_OUT[12]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FIFO_DATA_OUT[13]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FIFO_DATA_OUT[14]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD2_FIFO_DATA_OUT[15]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FLAG_SHOW[0]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FLAG_SHOW[1]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FLAG_SHOW[2]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FLAG_SHOW[3]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FLAG_SHOW[4]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FLAG_SHOW[5]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FLAG_SHOW[6]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FLAG_SHOW[7]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FLAG_SHOW[8]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FLAG_SHOW[9]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FLAG_SHOW[10]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FLAG_SHOW[11]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FLAG_SHOW[12]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FLAG_SHOW[13]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FLAG_SHOW[14]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FLAG_SHOW[15]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FIFO_DATA_OUT[0]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FIFO_DATA_OUT[1]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FIFO_DATA_OUT[2]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FIFO_DATA_OUT[3]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FIFO_DATA_OUT[4]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FIFO_DATA_OUT[5]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FIFO_DATA_OUT[6]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FIFO_DATA_OUT[7]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FIFO_DATA_OUT[8]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FIFO_DATA_OUT[9]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FIFO_DATA_OUT[10]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FIFO_DATA_OUT[11]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FIFO_DATA_OUT[12]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FIFO_DATA_OUT[13]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FIFO_DATA_OUT[14]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (10041): Inferred latch for "AD1_FIFO_DATA_OUT[15]" at AD_DATA_DEAL.v(46) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_DATA_DEAL.v Line: 46
Info (12128): Elaborating entity "TYFIFO" for hierarchy "TYFIFO:u_AD1_FIFO"
Info (12128): Elaborating entity "dcfifo" for hierarchy "TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/TYFIFO/TYFIFO.v Line: 75
Info (12130): Elaborated megafunction instantiation "TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/TYFIFO/TYFIFO.v Line: 75
Info (12133): Instantiated megafunction "TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component" with the following parameter: File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/TYFIFO/TYFIFO.v Line: 75
    Info (12134): Parameter "intended_device_family" = "Cyclone IV E"
    Info (12134): Parameter "lpm_numwords" = "1024"
    Info (12134): Parameter "lpm_showahead" = "OFF"
    Info (12134): Parameter "lpm_type" = "dcfifo"
    Info (12134): Parameter "lpm_width" = "12"
    Info (12134): Parameter "lpm_widthu" = "10"
    Info (12134): Parameter "overflow_checking" = "ON"
    Info (12134): Parameter "rdsync_delaypipe" = "4"
    Info (12134): Parameter "underflow_checking" = "ON"
    Info (12134): Parameter "use_eab" = "ON"
    Info (12134): Parameter "wrsync_delaypipe" = "4"
Info (12021): Found 1 design units, including 1 entities, in source file db/dcfifo_vve1.tdf
    Info (12023): Found entity 1: dcfifo_vve1 File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/dcfifo_vve1.tdf Line: 36
Info (12128): Elaborating entity "dcfifo_vve1" for hierarchy "TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated" File: g:/altera/18.1/quartus/libraries/megafunctions/dcfifo.tdf Line: 190
Info (12021): Found 1 design units, including 1 entities, in source file db/a_graycounter_4p6.tdf
    Info (12023): Found entity 1: a_graycounter_4p6 File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/a_graycounter_4p6.tdf Line: 24
Info (12128): Elaborating entity "a_graycounter_4p6" for hierarchy "TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|a_graycounter_4p6:rdptr_g1p" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/dcfifo_vve1.tdf Line: 47
Info (12021): Found 1 design units, including 1 entities, in source file db/a_graycounter_07c.tdf
    Info (12023): Found entity 1: a_graycounter_07c File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/a_graycounter_07c.tdf Line: 24
Info (12128): Elaborating entity "a_graycounter_07c" for hierarchy "TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|a_graycounter_07c:wrptr_g1p" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/dcfifo_vve1.tdf Line: 48
Info (12021): Found 1 design units, including 1 entities, in source file db/altsyncram_ce41.tdf
    Info (12023): Found entity 1: altsyncram_ce41 File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/altsyncram_ce41.tdf Line: 27
Info (12128): Elaborating entity "altsyncram_ce41" for hierarchy "TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|altsyncram_ce41:fifo_ram" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/dcfifo_vve1.tdf Line: 49
Info (12021): Found 1 design units, including 1 entities, in source file db/alt_synch_pipe_qal.tdf
    Info (12023): Found entity 1: alt_synch_pipe_qal File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/alt_synch_pipe_qal.tdf Line: 26
Info (12128): Elaborating entity "alt_synch_pipe_qal" for hierarchy "TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/dcfifo_vve1.tdf Line: 56
Info (12021): Found 1 design units, including 1 entities, in source file db/dffpipe_b09.tdf
    Info (12023): Found entity 1: dffpipe_b09 File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/dffpipe_b09.tdf Line: 24
Info (12128): Elaborating entity "dffpipe_b09" for hierarchy "TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/alt_synch_pipe_qal.tdf Line: 33
Info (12021): Found 1 design units, including 1 entities, in source file db/alt_synch_pipe_ral.tdf
    Info (12023): Found entity 1: alt_synch_pipe_ral File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/alt_synch_pipe_ral.tdf Line: 26
Info (12128): Elaborating entity "alt_synch_pipe_ral" for hierarchy "TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/dcfifo_vve1.tdf Line: 57
Info (12021): Found 1 design units, including 1 entities, in source file db/dffpipe_c09.tdf
    Info (12023): Found entity 1: dffpipe_c09 File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/dffpipe_c09.tdf Line: 24
Info (12128): Elaborating entity "dffpipe_c09" for hierarchy "TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/alt_synch_pipe_ral.tdf Line: 33
Info (12021): Found 1 design units, including 1 entities, in source file db/cmpr_o76.tdf
    Info (12023): Found entity 1: cmpr_o76 File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/cmpr_o76.tdf Line: 22
Info (12128): Elaborating entity "cmpr_o76" for hierarchy "TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|cmpr_o76:rdempty_eq_comp" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/dcfifo_vve1.tdf Line: 58
Info (12128): Elaborating entity "FREQ_DEV" for hierarchy "FREQ_DEV:u_AD1_DEV"
Info (12128): Elaborating entity "AD_FREQ_WORD" for hierarchy "AD_FREQ_WORD:u_AD_FREQ_WORD"
Warning (10270): Verilog HDL Case Statement warning at AD_FREQ_WORD.v(51): incomplete case statement has no default case item File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 51
Warning (10240): Verilog HDL Always Construct warning at AD_FREQ_WORD.v(49): inferring latch(es) for variable "AD1_OUTH", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Warning (10240): Verilog HDL Always Construct warning at AD_FREQ_WORD.v(49): inferring latch(es) for variable "AD1_OUTL", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Warning (10240): Verilog HDL Always Construct warning at AD_FREQ_WORD.v(49): inferring latch(es) for variable "AD2_OUTH", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Warning (10240): Verilog HDL Always Construct warning at AD_FREQ_WORD.v(49): inferring latch(es) for variable "AD2_OUTL", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTL[0]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTL[1]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTL[2]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTL[3]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTL[4]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTL[5]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTL[6]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTL[7]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTL[8]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTL[9]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTL[10]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTL[11]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTL[12]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTL[13]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTL[14]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTL[15]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTH[0]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTH[1]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTH[2]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTH[3]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTH[4]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTH[5]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTH[6]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTH[7]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTH[8]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTH[9]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTH[10]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTH[11]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTH[12]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTH[13]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTH[14]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD2_OUTH[15]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTL[0]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTL[1]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTL[2]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTL[3]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTL[4]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTL[5]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTL[6]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTL[7]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTL[8]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTL[9]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTL[10]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTL[11]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTL[12]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTL[13]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTL[14]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTL[15]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTH[0]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTH[1]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTH[2]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTH[3]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTH[4]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTH[5]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTH[6]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTH[7]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTH[8]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTH[9]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTH[10]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTH[11]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTH[12]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTH[13]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTH[14]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (10041): Inferred latch for "AD1_OUTH[15]" at AD_FREQ_WORD.v(49) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/AD_FREQ_WORD.v Line: 49
Info (12128): Elaborating entity "DA_PARAMETER_DEAL" for hierarchy "DA_PARAMETER_DEAL:inst1"
Warning (10270): Verilog HDL Case Statement warning at DA_PARAMETER_DEAL.v(43): incomplete case statement has no default case item File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 43
Warning (10240): Verilog HDL Always Construct warning at DA_PARAMETER_DEAL.v(42): inferring latch(es) for variable "DA1_OUTH", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Warning (10240): Verilog HDL Always Construct warning at DA_PARAMETER_DEAL.v(42): inferring latch(es) for variable "DA1_OUTL", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Warning (10240): Verilog HDL Always Construct warning at DA_PARAMETER_DEAL.v(42): inferring latch(es) for variable "DA2_OUTH", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Warning (10240): Verilog HDL Always Construct warning at DA_PARAMETER_DEAL.v(42): inferring latch(es) for variable "DA2_OUTL", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Warning (10240): Verilog HDL Always Construct warning at DA_PARAMETER_DEAL.v(42): inferring latch(es) for variable "DA1_AMP_OUT", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Warning (10240): Verilog HDL Always Construct warning at DA_PARAMETER_DEAL.v(42): inferring latch(es) for variable "DA2_AMP_OUT", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Warning (10240): Verilog HDL Always Construct warning at DA_PARAMETER_DEAL.v(42): inferring latch(es) for variable "DA1_STEP_OUT", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Warning (10240): Verilog HDL Always Construct warning at DA_PARAMETER_DEAL.v(42): inferring latch(es) for variable "DA2_STEP_OUT", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Warning (10240): Verilog HDL Always Construct warning at DA_PARAMETER_DEAL.v(42): inferring latch(es) for variable "DA1_WAVE_OUT", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Warning (10240): Verilog HDL Always Construct warning at DA_PARAMETER_DEAL.v(42): inferring latch(es) for variable "DA2_WAVE_OUT", which holds its previous value in one or more paths through the always construct File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_WAVE_OUT[0]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_WAVE_OUT[1]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_WAVE_OUT[2]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_WAVE_OUT[3]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_WAVE_OUT[4]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_WAVE_OUT[5]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_WAVE_OUT[6]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_WAVE_OUT[7]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_WAVE_OUT[0]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_WAVE_OUT[1]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_WAVE_OUT[2]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_WAVE_OUT[3]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_WAVE_OUT[4]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_WAVE_OUT[5]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_WAVE_OUT[6]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_WAVE_OUT[7]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_STEP_OUT[0]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_STEP_OUT[1]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_STEP_OUT[2]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_STEP_OUT[3]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_STEP_OUT[4]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_STEP_OUT[5]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_STEP_OUT[6]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_STEP_OUT[7]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_STEP_OUT[0]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_STEP_OUT[1]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_STEP_OUT[2]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_STEP_OUT[3]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_STEP_OUT[4]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_STEP_OUT[5]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_STEP_OUT[6]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_STEP_OUT[7]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_AMP_OUT[0]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_AMP_OUT[1]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_AMP_OUT[2]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_AMP_OUT[3]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_AMP_OUT[4]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_AMP_OUT[5]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_AMP_OUT[6]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_AMP_OUT[7]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_AMP_OUT[8]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_AMP_OUT[9]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_AMP_OUT[10]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_AMP_OUT[11]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_AMP_OUT[0]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_AMP_OUT[1]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_AMP_OUT[2]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_AMP_OUT[3]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_AMP_OUT[4]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_AMP_OUT[5]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_AMP_OUT[6]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_AMP_OUT[7]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_AMP_OUT[8]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_AMP_OUT[9]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_AMP_OUT[10]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_AMP_OUT[11]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTL[0]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTL[1]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTL[2]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTL[3]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTL[4]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTL[5]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTL[6]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTL[7]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTL[8]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTL[9]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTL[10]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTL[11]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTL[12]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTL[13]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTL[14]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTL[15]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTH[0]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTH[1]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTH[2]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTH[3]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTH[4]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTH[5]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTH[6]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTH[7]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTH[8]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTH[9]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTH[10]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTH[11]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTH[12]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTH[13]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTH[14]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA2_OUTH[15]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTL[0]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTL[1]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTL[2]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTL[3]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTL[4]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTL[5]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTL[6]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTL[7]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTL[8]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTL[9]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTL[10]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTL[11]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTL[12]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTL[13]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTL[14]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTL[15]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTH[0]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTH[1]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTH[2]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTH[3]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTH[4]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTH[5]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTH[6]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTH[7]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTH[8]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTH[9]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTH[10]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTH[11]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTH[12]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTH[13]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTH[14]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (10041): Inferred latch for "DA1_OUTH[15]" at DA_PARAMETER_DEAL.v(42) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_DEAL.v Line: 42
Info (12128): Elaborating entity "voltage_scaler_clocked_fast" for hierarchy "voltage_scaler_clocked_fast:inst10"
Warning (10036): Verilog HDL or VHDL warning at voltage_scaler_clocked_fast.v(22): object "rom_data_reg2" assigned a value but never read File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/voltage_scaler_clocked_fast.v Line: 22
Warning (10230): Verilog HDL assignment warning at voltage_scaler_clocked_fast.v(42): truncated value with size 32 to match size of target (14) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/voltage_scaler_clocked_fast.v Line: 42
Warning (10230): Verilog HDL assignment warning at voltage_scaler_clocked_fast.v(44): truncated value with size 32 to match size of target (14) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/voltage_scaler_clocked_fast.v Line: 44
Warning (10230): Verilog HDL assignment warning at voltage_scaler_clocked_fast.v(67): truncated value with size 32 to match size of target (14) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/voltage_scaler_clocked_fast.v Line: 67
Warning (10230): Verilog HDL assignment warning at voltage_scaler_clocked_fast.v(70): truncated value with size 32 to match size of target (14) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/voltage_scaler_clocked_fast.v Line: 70
Info (12128): Elaborating entity "DA_WAVEFORM" for hierarchy "DA_WAVEFORM:inst5"
Info (12128): Elaborating entity "sin_rom" for hierarchy "DA_WAVEFORM:inst5|sin_rom:sin_rom_inst" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_WAVEFORM.v Line: 48
Info (12128): Elaborating entity "altsyncram" for hierarchy "DA_WAVEFORM:inst5|sin_rom:sin_rom_inst|altsyncram:altsyncram_component" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/sin_rom/sin_rom.v Line: 81
Info (12130): Elaborated megafunction instantiation "DA_WAVEFORM:inst5|sin_rom:sin_rom_inst|altsyncram:altsyncram_component" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/sin_rom/sin_rom.v Line: 81
Info (12133): Instantiated megafunction "DA_WAVEFORM:inst5|sin_rom:sin_rom_inst|altsyncram:altsyncram_component" with the following parameter: File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/sin_rom/sin_rom.v Line: 81
    Info (12134): Parameter "address_aclr_a" = "NONE"
    Info (12134): Parameter "clock_enable_input_a" = "BYPASS"
    Info (12134): Parameter "clock_enable_output_a" = "BYPASS"
    Info (12134): Parameter "init_file" = "../script/sine_64x14.mif"
    Info (12134): Parameter "intended_device_family" = "Cyclone IV E"
    Info (12134): Parameter "lpm_hint" = "ENABLE_RUNTIME_MOD=NO"
    Info (12134): Parameter "lpm_type" = "altsyncram"
    Info (12134): Parameter "numwords_a" = "64"
    Info (12134): Parameter "operation_mode" = "ROM"
    Info (12134): Parameter "outdata_aclr_a" = "NONE"
    Info (12134): Parameter "outdata_reg_a" = "UNREGISTERED"
    Info (12134): Parameter "widthad_a" = "6"
    Info (12134): Parameter "width_a" = "14"
    Info (12134): Parameter "width_byteena_a" = "1"
Info (12021): Found 1 design units, including 1 entities, in source file db/altsyncram_hva1.tdf
    Info (12023): Found entity 1: altsyncram_hva1 File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/altsyncram_hva1.tdf Line: 27
Info (12128): Elaborating entity "altsyncram_hva1" for hierarchy "DA_WAVEFORM:inst5|sin_rom:sin_rom_inst|altsyncram:altsyncram_component|altsyncram_hva1:auto_generated" File: g:/altera/18.1/quartus/libraries/megafunctions/altsyncram.tdf Line: 791
Info (12128): Elaborating entity "sqaure_rom" for hierarchy "DA_WAVEFORM:inst5|sqaure_rom:sqaure_rom_inst" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_WAVEFORM.v Line: 54
Info (12128): Elaborating entity "altsyncram" for hierarchy "DA_WAVEFORM:inst5|sqaure_rom:sqaure_rom_inst|altsyncram:altsyncram_component" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/sqaure_rom/sqaure_rom.v Line: 81
Info (12130): Elaborated megafunction instantiation "DA_WAVEFORM:inst5|sqaure_rom:sqaure_rom_inst|altsyncram:altsyncram_component" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/sqaure_rom/sqaure_rom.v Line: 81
Info (12133): Instantiated megafunction "DA_WAVEFORM:inst5|sqaure_rom:sqaure_rom_inst|altsyncram:altsyncram_component" with the following parameter: File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/sqaure_rom/sqaure_rom.v Line: 81
    Info (12134): Parameter "address_aclr_a" = "NONE"
    Info (12134): Parameter "clock_enable_input_a" = "BYPASS"
    Info (12134): Parameter "clock_enable_output_a" = "BYPASS"
    Info (12134): Parameter "init_file" = "../script/square_64x14.mif"
    Info (12134): Parameter "intended_device_family" = "Cyclone IV E"
    Info (12134): Parameter "lpm_hint" = "ENABLE_RUNTIME_MOD=NO"
    Info (12134): Parameter "lpm_type" = "altsyncram"
    Info (12134): Parameter "numwords_a" = "64"
    Info (12134): Parameter "operation_mode" = "ROM"
    Info (12134): Parameter "outdata_aclr_a" = "NONE"
    Info (12134): Parameter "outdata_reg_a" = "UNREGISTERED"
    Info (12134): Parameter "widthad_a" = "6"
    Info (12134): Parameter "width_a" = "14"
    Info (12134): Parameter "width_byteena_a" = "1"
Info (12021): Found 1 design units, including 1 entities, in source file db/altsyncram_j6b1.tdf
    Info (12023): Found entity 1: altsyncram_j6b1 File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/altsyncram_j6b1.tdf Line: 27
Info (12128): Elaborating entity "altsyncram_j6b1" for hierarchy "DA_WAVEFORM:inst5|sqaure_rom:sqaure_rom_inst|altsyncram:altsyncram_component|altsyncram_j6b1:auto_generated" File: g:/altera/18.1/quartus/libraries/megafunctions/altsyncram.tdf Line: 791
Info (12128): Elaborating entity "triangle_rom" for hierarchy "DA_WAVEFORM:inst5|triangle_rom:triangle_rom_inst" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_WAVEFORM.v Line: 60
Info (12128): Elaborating entity "altsyncram" for hierarchy "DA_WAVEFORM:inst5|triangle_rom:triangle_rom_inst|altsyncram:altsyncram_component" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/triangle_rom/triangle_rom.v Line: 81
Info (12130): Elaborated megafunction instantiation "DA_WAVEFORM:inst5|triangle_rom:triangle_rom_inst|altsyncram:altsyncram_component" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/triangle_rom/triangle_rom.v Line: 81
Info (12133): Instantiated megafunction "DA_WAVEFORM:inst5|triangle_rom:triangle_rom_inst|altsyncram:altsyncram_component" with the following parameter: File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/triangle_rom/triangle_rom.v Line: 81
    Info (12134): Parameter "address_aclr_a" = "NONE"
    Info (12134): Parameter "clock_enable_input_a" = "BYPASS"
    Info (12134): Parameter "clock_enable_output_a" = "BYPASS"
    Info (12134): Parameter "init_file" = "../script/triangle_64x14.mif"
    Info (12134): Parameter "intended_device_family" = "Cyclone IV E"
    Info (12134): Parameter "lpm_hint" = "ENABLE_RUNTIME_MOD=NO"
    Info (12134): Parameter "lpm_type" = "altsyncram"
    Info (12134): Parameter "numwords_a" = "64"
    Info (12134): Parameter "operation_mode" = "ROM"
    Info (12134): Parameter "outdata_aclr_a" = "NONE"
    Info (12134): Parameter "outdata_reg_a" = "UNREGISTERED"
    Info (12134): Parameter "widthad_a" = "6"
    Info (12134): Parameter "width_a" = "14"
    Info (12134): Parameter "width_byteena_a" = "1"
Info (12021): Found 1 design units, including 1 entities, in source file db/altsyncram_ocb1.tdf
    Info (12023): Found entity 1: altsyncram_ocb1 File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/altsyncram_ocb1.tdf Line: 27
Info (12128): Elaborating entity "altsyncram_ocb1" for hierarchy "DA_WAVEFORM:inst5|triangle_rom:triangle_rom_inst|altsyncram:altsyncram_component|altsyncram_ocb1:auto_generated" File: g:/altera/18.1/quartus/libraries/megafunctions/altsyncram.tdf Line: 791
Info (12128): Elaborating entity "sawtooth_rom" for hierarchy "DA_WAVEFORM:inst5|sawtooth_rom:sawtooth_rom_inst" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_WAVEFORM.v Line: 66
Info (12128): Elaborating entity "altsyncram" for hierarchy "DA_WAVEFORM:inst5|sawtooth_rom:sawtooth_rom_inst|altsyncram:altsyncram_component" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/sawtooth_rom/sawtooth_rom.v Line: 81
Info (12130): Elaborated megafunction instantiation "DA_WAVEFORM:inst5|sawtooth_rom:sawtooth_rom_inst|altsyncram:altsyncram_component" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/sawtooth_rom/sawtooth_rom.v Line: 81
Info (12133): Instantiated megafunction "DA_WAVEFORM:inst5|sawtooth_rom:sawtooth_rom_inst|altsyncram:altsyncram_component" with the following parameter: File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/ip/sawtooth_rom/sawtooth_rom.v Line: 81
    Info (12134): Parameter "address_aclr_a" = "NONE"
    Info (12134): Parameter "clock_enable_input_a" = "BYPASS"
    Info (12134): Parameter "clock_enable_output_a" = "BYPASS"
    Info (12134): Parameter "init_file" = "../script/sawtooth_64x14.mif"
    Info (12134): Parameter "intended_device_family" = "Cyclone IV E"
    Info (12134): Parameter "lpm_hint" = "ENABLE_RUNTIME_MOD=NO"
    Info (12134): Parameter "lpm_type" = "altsyncram"
    Info (12134): Parameter "numwords_a" = "64"
    Info (12134): Parameter "operation_mode" = "ROM"
    Info (12134): Parameter "outdata_aclr_a" = "NONE"
    Info (12134): Parameter "outdata_reg_a" = "UNREGISTERED"
    Info (12134): Parameter "widthad_a" = "6"
    Info (12134): Parameter "width_a" = "14"
    Info (12134): Parameter "width_byteena_a" = "1"
Info (12021): Found 1 design units, including 1 entities, in source file db/altsyncram_rdb1.tdf
    Info (12023): Found entity 1: altsyncram_rdb1 File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/altsyncram_rdb1.tdf Line: 27
Info (12128): Elaborating entity "altsyncram_rdb1" for hierarchy "DA_WAVEFORM:inst5|sawtooth_rom:sawtooth_rom_inst|altsyncram:altsyncram_component|altsyncram_rdb1:auto_generated" File: g:/altera/18.1/quartus/libraries/megafunctions/altsyncram.tdf Line: 791
Info (12128): Elaborating entity "VOLTAGE_SCALER_CLOCKED" for hierarchy "VOLTAGE_SCALER_CLOCKED:inst11"
Warning (10230): Verilog HDL assignment warning at VOLTAGE_SCALER_CLOCKED.v(41): truncated value with size 32 to match size of target (14) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/VOLTAGE_SCALER_CLOCKED.v Line: 41
Warning (10230): Verilog HDL assignment warning at VOLTAGE_SCALER_CLOCKED.v(47): truncated value with size 32 to match size of target (14) File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/VOLTAGE_SCALER_CLOCKED.v Line: 47
Warning (12020): Port "address" on the entity instantiation of "sawtooth_rom_inst" is connected to a signal of width 7. The formal width of the signal in the module is 6.  The extra bits will be ignored. File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_WAVEFORM.v Line: 66
Warning (12020): Port "address" on the entity instantiation of "triangle_rom_inst" is connected to a signal of width 7. The formal width of the signal in the module is 6.  The extra bits will be ignored. File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_WAVEFORM.v Line: 60
Warning (12020): Port "address" on the entity instantiation of "sqaure_rom_inst" is connected to a signal of width 7. The formal width of the signal in the module is 6.  The extra bits will be ignored. File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_WAVEFORM.v Line: 54
Warning (12020): Port "address" on the entity instantiation of "sin_rom_inst" is connected to a signal of width 7. The formal width of the signal in the module is 6.  The extra bits will be ignored. File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_WAVEFORM.v Line: 48
Warning (12001): Port "PHASE_ADDR[7]" does not exist in entity definition of "DA_WAVEFORM".  The port's range differs between the entity definition and its actual instantiation, "DA_WAVEFORM:inst8".
Warning (12001): Port "PHASE_ADDR[7]" does not exist in entity definition of "DA_WAVEFORM".  The port's range differs between the entity definition and its actual instantiation, "DA_WAVEFORM:inst5".
Warning (19016): Clock multiplexers are found and protected
    Warning (19017): Found clock multiplexer DA_PARAMETER_CTRL:inst7|FREQ_OUT_B_FINAL File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/DA_PARAMETER_CTRL.v Line: 60
Info (278001): Inferred 4 megafunctions from design logic
    Info (278003): Inferred multiplier megafunction ("lpm_mult") from the following logic: "voltage_scaler_clocked_fast:inst10|Mult1" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/voltage_scaler_clocked_fast.v Line: 70
    Info (278003): Inferred multiplier megafunction ("lpm_mult") from the following logic: "voltage_scaler_clocked_fast:inst14|Mult1" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/voltage_scaler_clocked_fast.v Line: 70
    Info (278003): Inferred multiplier megafunction ("lpm_mult") from the following logic: "voltage_scaler_clocked_fast:inst10|Mult0" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/voltage_scaler_clocked_fast.v Line: 48
    Info (278003): Inferred multiplier megafunction ("lpm_mult") from the following logic: "voltage_scaler_clocked_fast:inst14|Mult0" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/voltage_scaler_clocked_fast.v Line: 48
Info (12130): Elaborated megafunction instantiation "voltage_scaler_clocked_fast:inst10|lpm_mult:Mult1" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/voltage_scaler_clocked_fast.v Line: 70
Info (12133): Instantiated megafunction "voltage_scaler_clocked_fast:inst10|lpm_mult:Mult1" with the following parameter: File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/voltage_scaler_clocked_fast.v Line: 70
    Info (12134): Parameter "LPM_WIDTHA" = "26"
    Info (12134): Parameter "LPM_WIDTHB" = "7"
    Info (12134): Parameter "LPM_WIDTHP" = "33"
    Info (12134): Parameter "LPM_WIDTHR" = "33"
    Info (12134): Parameter "LPM_WIDTHS" = "1"
    Info (12134): Parameter "LPM_REPRESENTATION" = "UNSIGNED"
    Info (12134): Parameter "INPUT_A_IS_CONSTANT" = "NO"
    Info (12134): Parameter "INPUT_B_IS_CONSTANT" = "YES"
    Info (12134): Parameter "MAXIMIZE_SPEED" = "5"
Info (12021): Found 1 design units, including 1 entities, in source file db/mult_cft.tdf
    Info (12023): Found entity 1: mult_cft File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/mult_cft.tdf Line: 30
Info (12130): Elaborated megafunction instantiation "voltage_scaler_clocked_fast:inst10|lpm_mult:Mult0" File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/voltage_scaler_clocked_fast.v Line: 48
Info (12133): Instantiated megafunction "voltage_scaler_clocked_fast:inst10|lpm_mult:Mult0" with the following parameter: File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/src/voltage_scaler_clocked_fast.v Line: 48
    Info (12134): Parameter "LPM_WIDTHA" = "14"
    Info (12134): Parameter "LPM_WIDTHB" = "12"
    Info (12134): Parameter "LPM_WIDTHP" = "26"
    Info (12134): Parameter "LPM_WIDTHR" = "26"
    Info (12134): Parameter "LPM_WIDTHS" = "1"
    Info (12134): Parameter "LPM_REPRESENTATION" = "UNSIGNED"
    Info (12134): Parameter "INPUT_A_IS_CONSTANT" = "NO"
    Info (12134): Parameter "INPUT_B_IS_CONSTANT" = "NO"
    Info (12134): Parameter "MAXIMIZE_SPEED" = "5"
Info (12021): Found 1 design units, including 1 entities, in source file db/mult_4dt.tdf
    Info (12023): Found entity 1: mult_4dt File: C:/Users/<USER>/Downloads/zuolan_v4.1 (1)/zuolan_FPGA/prj/db/mult_4dt.tdf Line: 28
Warning (12241): 4 hierarchies have connectivity warnings - see the Connectivity Checks report folder
Info (13014): Ignored 70 buffer(s)
    Info (13019): Ignored 70 SOFT buffer(s)
Info (286030): Timing-Driven Synthesis is running
Info (17049): 4 registers lost all their fanouts during netlist optimizations.
Info (16010): Generating hard_block partition "hard_block:auto_generated_inst"
    Info (16011): Adding 1 node(s), including 0 DDIO, 1 PLL, 0 transceiver and 0 LCELL
Info (21057): Implemented 2214 device resources after synthesis - the final resource count might be different
    Info (21058): Implemented 32 input pins
    Info (21059): Implemented 32 output pins
    Info (21060): Implemented 16 bidirectional pins
    Info (21061): Implemented 1987 logic cells
    Info (21064): Implemented 136 RAM segments
    Info (21065): Implemented 1 PLLs
    Info (21062): Implemented 10 DSP elements
Info: Quartus Prime Analysis & Synthesis was successful. 0 errors, 61 warnings
    Info: Peak virtual memory: 4891 megabytes
    Info: Processing ended: Fri Aug 01 09:24:09 2025
    Info: Elapsed time: 00:00:08
    Info: Total CPU time (on all processors): 00:00:14


