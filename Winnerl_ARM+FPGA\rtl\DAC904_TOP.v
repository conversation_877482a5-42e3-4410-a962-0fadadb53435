module DAC904_TOP(
	input  wire 			 SYS_CLK	,
	input	 wire 			 SYS_RST	,
	input	 wire	[2:0]	 KEY_IN		,
	output wire				 PD				,
	output wire				 DAC_CLK	,
	output wire	[13:0] DAC_DATA
);

//DAC904的输出电压转换公式：(16383 - DAC_DATA) * （6V/16383），此公式默认不使用输出放大缩小，短路帽接12

wire	CLK_165M;

wire 	[1 :0] sel			;
wire 	[31:0] fre_k		;
wire 	[11:0] addr			;
wire 	[13:0] wave_z		;
wire 	[13:0] wave_s		;
wire 	[13:0] wave_f		;
wire	[13:0] DATA_BUF	;

// 方波数据
assign wave_f = addr[11] ? 14'b11_1111_1111_1111 : 14'b00_0000_0000_0000;

//锁相环模块
PLL_CLK u_PLL_CLK(
	.areset	(!SYS_RST	),
	.inclk0	(SYS_CLK	),
	.c0			(CLK_165M	)
);

// 正弦波数据，ROM表
ROM_Sin u_ROM_Sin(
	.address(addr			),
	.clock	(CLK_165M	),
	.q			(wave_z		)
);

// 三角波数据，ROM表
ROM_Tri ROM_Tri(
	.address(addr			),
	.clock	(CLK_165M	),
	.q			(wave_s		)
);

//地址累加器
add_32bit u_add_32bit(
	.clk	(CLK_165M	),
	.rst	(SYS_RST	),
	.fr_k (fre_k		),
	.adder(addr			)
);

//按键控制模块
key_con u_key_con(
	.clk			(CLK_165M	), 
	.rst_n		(SYS_RST	),
	.key1_in	(KEY_IN[0]),
	.key2_in	(KEY_IN[1]),
	.key3_in  (KEY_IN[2]),
	.sel_wave	(sel			),
	.fre_k		(fre_k		)
);

//波形输出选择模块
sel_wave u_sel_wave(
	.clk		(CLK_165M	),
	.rst_n	(SYS_RST	),
	.sel		(sel			),
	.da_ina (wave_z		),
	.da_inb (wave_s		),
	.da_inc (wave_f		),
	.da_out (DATA_BUF	)
);

assign PD = 1'b0;
assign DAC_CLK  = CLK_165M;
assign DAC_DATA = DATA_BUF; // 交流输出
//assign DAC_DATA = 14'd10000; // 直流测试，0.66V左右

endmodule
