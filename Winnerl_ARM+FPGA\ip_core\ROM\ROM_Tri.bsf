/*
WARNING: Do NOT edit the input and output ports in this file in a text
editor if you plan to continue editing the block that represents it in
the Block Editor! File corruption is VERY likely to occur.
*/
/*
Copyright (C) 1991-2013 Altera Corporation
Your use of Altera Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Altera Program License 
Subscription Agreement, Altera MegaCore Function License 
Agreement, or other applicable license agreement, including, 
without limitation, that your use is for the sole purpose of 
programming logic devices manufactured by Altera and sold by 
Altera or its authorized distributors.  Please refer to the 
applicable agreement for further details.
*/
(header "symbol" (version "1.2"))
(symbol
	(rect 0 0 216 128)
	(text "ROM_Tri" (rect 83 0 143 16)(font "Arial" (font_size 10)))
	(text "inst" (rect 8 112 25 124)(font "Arial" ))
	(port
		(pt 0 32)
		(input)
		(text "address[11..0]" (rect 0 0 81 14)(font "Arial" (font_size 8)))
		(text "address[11..0]" (rect 4 18 71 31)(font "Arial" (font_size 8)))
		(line (pt 0 32)(pt 88 32)(line_width 3))
	)
	(port
		(pt 0 112)
		(input)
		(text "clock" (rect 0 0 29 14)(font "Arial" (font_size 8)))
		(text "clock" (rect 4 98 27 111)(font "Arial" (font_size 8)))
		(line (pt 0 112)(pt 80 112))
	)
	(port
		(pt 216 32)
		(output)
		(text "q[13..0]" (rect 0 0 42 14)(font "Arial" (font_size 8)))
		(text "q[13..0]" (rect 177 18 211 31)(font "Arial" (font_size 8)))
		(line (pt 216 32)(pt 136 32)(line_width 3))
	)
	(drawing
		(text "14 bits" (rect 109 24 194 159)(font "Arial" )(vertical))
		(text "4096 words" (rect 120 12 214 177)(font "Arial" )(vertical))
		(text "Block type: AUTO" (rect 48 114 170 239)(font "Arial" ))
		(line (pt 104 24)(pt 136 24))
		(line (pt 136 24)(pt 136 96))
		(line (pt 136 96)(pt 104 96))
		(line (pt 104 96)(pt 104 24))
		(line (pt 118 58)(pt 123 63))
		(line (pt 118 62)(pt 123 57))
		(line (pt 88 27)(pt 96 27))
		(line (pt 96 27)(pt 96 39))
		(line (pt 96 39)(pt 88 39))
		(line (pt 88 39)(pt 88 27))
		(line (pt 88 34)(pt 90 36))
		(line (pt 90 36)(pt 88 38))
		(line (pt 80 36)(pt 88 36))
		(line (pt 96 32)(pt 104 32)(line_width 3))
		(line (pt 80 112)(pt 80 36))
		(line (pt 0 0)(pt 217 0))
		(line (pt 217 0)(pt 217 130))
		(line (pt 0 130)(pt 217 130))
		(line (pt 0 0)(pt 0 130))
		(line (pt 0 0)(pt 0 0))
		(line (pt 0 0)(pt 0 0))
		(line (pt 0 0)(pt 0 0))
		(line (pt 0 0)(pt 0 0))
	)
)
